import { draw } from "@/plugins/svgjs";
import {
  addElementToSelection,
  addSegmentToSelection,
  addWorkspaceToCurrentWorkspace,
  blinkValidation,
  cloneSelectedElements,
  collapseWorkspace,
  configureElementAnchors,
  createAnchors,
  createConnectionBetweenElements,
  createNewElementAtPosition,
  createWorkspace,
  deepClone,
  deleteElement,
  deleteSegment,
  deleteSelectedElements,
  diagramMode,
  elementIdsAvailable,
  endDragging,
  enterWorkspaceWithZoom,
  expandWorkspace,
  getPositionFromGraphicElement,
  hideItemPositions,
  highlightElement,
  isEmptyObject,
  isWorkspaceExpanded,
  moveElementToWorkspace,
  moveSelectedElementsToWorkspace,
  redo,
  removeElementFromSelection,
  resizeExpandedWorkspace,
  retraceAllSegments,
  saveWorkspace,
  selectAllElements,
  selectionMode,
  setAppropriateSelectionMode,
  showItemPositions,
  snapElementToGrid,
  startDragging,
  traceSegment,
  undo,
  unlightElement,
  unselectAllElements,
  unselectAllSegments,
  unselectElement,
  updateColorForSelectedElements,
  updateExpandedWorkspaceElementPosition,
  updateGraphicElementFromState,
  updateTextPropertiesForSelectedElements,
} from "@/core";
import {
  ELEMENT_POSITION,
  isElementFromImportedSequence,
  updateElementOpacity,
  updateElementPositionWithLazyLoading,
  updateInitialSequencesAfterElementUpdateInCreationMode,
  updatePosition,
} from "@/sequence";
import { store } from "@/store";
import {
  pauseSequence,
  playSequence,
  setDisplayToggleFromCurrentVisibility,
  setElementOpacity,
} from "@/animation";
import { drawAdapter } from "@/draw-adapter";

export const setupAnchorsListeners = (anchor, element) => {
  anchor
    .on("mouseover", () => {
      if (store.state.diagramMode !== diagramMode.creationMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      anchor.attr({ cursor: "pointer" });
      anchor.animate(100).size(10);
    })
    .on("mouseleave", () => {
      if (store.state.diagramMode !== diagramMode.creationMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      anchor.animate(100).size(5);
    })
    .on("dragmove", (e) => {
      e.preventDefault();
      if (store.state.diagramMode !== diagramMode.creationMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }

      const dragParams = store.state.dragParams;
      dragParams.dragFromAnchorStarted = true;
      dragParams.dragFromAnchorElementId = element.id;
      store.dispatch("updateDragParamsAction", dragParams);

      if (element.graphicElement.graphicTempLink) {
        element.graphicElement.graphicTempLink.remove();
      }
      if (element.graphicElement.graphicDestinationDummy) {
        element.graphicElement.graphicDestinationDummy.remove();
      }
      if (!store.state.dragParams.isHoveringAnotherElement) {
        let rectWidth, rectHeight;
        if (store.state.defaultShape === "TEXT-CLASSIC") {
          rectWidth = 200;
          rectHeight = 50;
        } else {
          rectWidth = 100;
          rectHeight = 100;
        }
        element.graphicElement.graphicDestinationDummy = draw
          .rect(rectWidth, rectHeight)
          .fill({
            color: "#ffffff90",
          })
          .stroke({
            color: "#000000",
            width: 3,
            dasharray: 3,
          })
          .center(e.detail.box.x2, e.detail.box.y2);
        element.graphicElement.graphicDestinationDummy.back();
        element.graphicElement.graphicDestinationDummy.forward();
      }
      element.graphicElement.graphicTempLink = draw
        .line(anchor.cx(), anchor.cy(), e.detail.box.x2, e.detail.box.y2)
        .stroke({
          color: "#a5a5a5",
          width: 3,
          dasharray: 3,
        });
      element.graphicElement.graphicTempLink.back();
      element.graphicElement.graphicTempLink.forward();
    })
    .on("dragend", async (e) => {
      if (store.state.diagramMode !== diagramMode.creationMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      const point = draw.point(e.detail.event.pageX, e.detail.event.pageY);
      element.graphicElement.graphicTempLink?.remove();
      element.graphicElement.graphicDestinationDummy?.remove();
      const dragParams = store.state.dragParams;
      dragParams.dragFromAnchorStarted = false;
      dragParams.dragFromAnchorElementId = null;
      if (anchor.inside(point.x, point.y)) {
        return;
      }
      if (dragParams.dragDestinationElement === element.id) {
        return;
      }

      if (dragParams.dragDestinationElement == null) {
        let newElement = await createNewElementAtPosition(point.x, point.y);
        dragParams.dragDestinationElement = newElement.id;
      }

      createConnectionBetweenElements(
        element.id,
        dragParams.dragDestinationElement,
        true,
      );
      store.dispatch("updateDragParamsAction", dragParams);
    });
};

// Helper function to create and show context menu
const showContextMenu = (e, x, y) => {
  // Remove any existing context menu
  hideContextMenu();

  // Menu dimensions
  const menuWidth = 180;
  const submenuWidth = 150;
  const itemHeight = 30;

  // Get all available workspaces that can be added to the current workspace
  const availableWorkspaces = store.getters.workspaces.filter((workspace) => {
    // Don't include root workspace, current workspace, or workspaces already in current workspace
    return (
      workspace.id !== store.state.rootWorkspaceId &&
      workspace.id !== store.state.currentWorkspaceId &&
      !Array.from(store.state.allSimpleElements.values()).find(
        (element) =>
          element.childWorkspaceId === workspace.id &&
          element.parentWorkspaceIds.includes(store.state.currentWorkspaceId),
      )
    );
  });

  // Create menu sections
  const sections = [
    {
      title: "Create Element",
      icon: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z", // Plus icon
      items: [
        { label: "Rectangle", type: "SQUARE", icon: "M3 3h18v18H3V3z" },
        {
          label: "Circle",
          type: "CIRCLE",
          icon: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z",
        },
        {
          label: "Diamond",
          type: "RHOMBUS",
          icon: "M12 2L2 12l10 10 10-10L12 2z",
        },
        {
          label: "Text",
          type: "TEXT-CLASSIC",
          icon: "M5 4v3h5.5v12h3V7H19V4H5z",
        },
      ],
      hasSubmenu: true,
    },
    {
      title: "Add Workspace",
      icon: "M19 11h-4v4h-2v-4H9v-2h4V5h2v4h4v2z", // Workspace icon
      items: availableWorkspaces.map((workspace) => ({
        label: workspace.name,
        workspaceId: workspace.id,
        icon: "M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z", // Workspace icon
      })),
      hasSubmenu: availableWorkspaces.length > 0,
    },
    {
      title: "Select All",
      icon: "M3 5h2V3c-1.1 0-2 .9-2 2zm0 8h2v-2H3v2zm4 8h2v-2H7v2zM3 9h2V7H3v2zm10-6h-2v2h2V3zm6 0v2h2c0-1.1-.9-2-2-2zM5 21v-2H3c0 1.1.9 2 2 2zm-2-4h2v-2H3v2zM9 3H7v2h2V3zm2 18h2v-2h-2v2zm8-8h2v-2h-2v2zm0 8c1.1 0 2-.9 2-2h-2v2zm0-12h2V7h-2v2zm0 8h2v-2h-2v2zm-4 4h2v-2h-2v2zm0-16h2V3h-2v2z", // Selection icon
      items: [
        {
          label: "Select All",
          action: "selectAll",
          icon: "M3 5h2V3c-1.1 0-2 .9-2 2zm0 8h2v-2H3v2zm4 8h2v-2H7v2zM3 9h2V7H3v2zm10-6h-2v2h2V3zm6 0v2h2c0-1.1-.9-2-2-2zM5 21v-2H3c0 1.1.9 2 2 2zm-2-4h2v-2H3v2zM9 3H7v2h2V3zm2 18h2v-2h-2v2zm8-8h2v-2h-2v2zm0 8c1.1 0 2-.9 2-2h-2v2zm0-12h2V7h-2v2zm0 8h2v-2h-2v2zm-4 4h2v-2h-2v2zm0-16h2V3h-2v2z",
        },
      ],
      hasSubmenu: false,
    },
  ];

  // Filter out hidden sections and calculate menu height
  const visibleSections = sections.filter((section) => !section.hidden);
  const titleHeight = 40; // Title + separator
  const paddingBottom = 10;
  const menuHeight =
    titleHeight + visibleSections.length * (itemHeight + 10) + paddingBottom;

  // Create context menu container
  const contextMenu = draw.group().attr("id", "context-menu");

  // Create background for the menu first (so it's behind other elements)
  contextMenu
    .rect(menuWidth, menuHeight)
    .radius(8)
    .fill("#ffffff")
    .opacity(0.95)
    .stroke({ color: "#3c4365", width: 1 })
    .move(x, y);

  // Create menu title
  contextMenu
    .text("Menu")
    .font({ family: "Arial", size: 14, weight: "bold" })
    .fill("#3c4365")
    .move(x + 10, y + 10);

  // Add separator line
  contextMenu
    .line(x + 10, y + 30, x + menuWidth - 10, y + 30)
    .stroke({ color: "#e0e0e0", width: 1 });

  // Track active submenu
  let activeSubmenu = null;
  let activeSection = null;

  // Create menu sections
  let currentY = y + 40;

  // Store menu item positions for each section
  const sectionPositions = [];

  sections.forEach((section, sectionIndex) => {
    // Skip hidden sections
    if (section.hidden) return;

    // Store the position of this section
    sectionPositions[sectionIndex] = { x: x + 10, y: currentY - 5 };

    // Create section background for hover effect first (so it's behind other elements)
    // Make the rectangle fit within the menu borders
    // Use transparent fill by default, only show background on hover
    const sectionBg = contextMenu
      .rect(menuWidth - 2, itemHeight)
      .radius(4)
      .fill("transparent")
      .move(x + 1, sectionPositions[sectionIndex].y);

    // Section title
    const sectionTitle = contextMenu
      .text(section.title)
      .font({ family: "Arial", size: 12, weight: "bold" })
      .fill("#555555")
      .move(
        sectionPositions[sectionIndex].x + 25,
        sectionPositions[sectionIndex].y + 5,
      );

    // Section icon
    const sectionIcon = contextMenu
      .path(section.icon)
      .fill("#555555")
      .size(16, 16)
      .move(
        sectionPositions[sectionIndex].x + 2,
        sectionPositions[sectionIndex].y + 5,
      );

    // Add arrow icon if has submenu
    let arrowIcon = null;
    if (section.hasSubmenu) {
      arrowIcon = contextMenu
        .path("M10 17l5-5-5-5v10z") // Right arrow icon
        .fill("#555555")
        .size(12, 12)
        .move(
          sectionPositions[sectionIndex].x + menuWidth - 35,
          sectionPositions[sectionIndex].y + 5,
        );
    }

    // Function to show submenu
    const showSubmenu = () => {
      sectionBg.fill("#f0f0f0");

      // Hide previous submenu if different section
      if (activeSubmenu && activeSection !== sectionIndex) {
        activeSubmenu.remove();
        activeSubmenu = null;
      }

      // Show submenu if this section has one
      if (section.hasSubmenu && !activeSubmenu) {
        activeSection = sectionIndex;
        // Position submenu to the right of the menu item, aligned with its top
        // Use the exact position of the menu item from sectionPositions
        activeSubmenu = createSubmenu(
          section.items,
          sectionPositions[sectionIndex].x + menuWidth - 10,
          sectionPositions[sectionIndex].y,
          submenuWidth,
          e,
        );
      }
    };

    // Function to handle mouse leave
    const handleMouseLeave = (evt) => {
      sectionBg.fill("#ffffff");

      // Check if mouse moved to submenu
      if (activeSubmenu) {
        // Get submenu position - must match the calculation in showSubmenu
        const submenuX = sectionPositions[sectionIndex].x + menuWidth - 10;
        const submenuY = sectionPositions[sectionIndex].y;
        const submenuHeight = section.items.length * itemHeight + 10;

        // Only hide submenu if mouse didn't move to submenu area
        if (
          evt.clientX < submenuX ||
          evt.clientX > submenuX + submenuWidth ||
          evt.clientY < submenuY ||
          evt.clientY > submenuY + submenuHeight
        ) {
          setTimeout(() => {
            // Additional check to prevent flickering
            if (
              activeSubmenu &&
              !activeSubmenu.node.matches(":hover") &&
              !sectionBg.node.matches(":hover") &&
              !sectionTitle.node.matches(":hover") &&
              !sectionIcon.node.matches(":hover") &&
              (!arrowIcon || !arrowIcon.node.matches(":hover"))
            ) {
              activeSubmenu.remove();
              activeSubmenu = null;
            }
          }, 100);
        }
      }
    };

    // Add event listeners to all elements
    sectionBg.on("mouseover", showSubmenu);
    sectionBg.on("mouseleave", handleMouseLeave);

    // Add same event listeners to text and icon
    sectionTitle.on("mouseover", showSubmenu);
    sectionTitle.on("mouseleave", handleMouseLeave);

    sectionIcon.on("mouseover", showSubmenu);
    sectionIcon.on("mouseleave", handleMouseLeave);

    if (arrowIcon) {
      arrowIcon.on("mouseover", showSubmenu);
      arrowIcon.on("mouseleave", handleMouseLeave);
    }

    // Add click handler for non-submenu sections
    if (!section.hasSubmenu) {
      // Function to handle click on action items
      const handleActionClick = async () => {
        if (
          section.items &&
          section.items.length > 0 &&
          section.items[0].action === "selectAll"
        ) {
          await selectAllElements();
          hideContextMenu();
        }
      };

      // Add click handlers to background, title and icon
      sectionBg.on("click", handleActionClick);
      sectionTitle.on("click", handleActionClick);
      sectionIcon.on("click", handleActionClick);
    }

    // Increment currentY for the next section
    currentY += itemHeight + 10;
  });

  // Function to create submenu
  function createSubmenu(items, submenuX, submenuY, width, originalEvent) {
    const submenu = draw.group().attr("id", "context-submenu");

    // Create submenu background
    submenu
      .rect(width, items.length * itemHeight + 10)
      .radius(8)
      .fill("#ffffff")
      .opacity(0.95)
      .stroke({ color: "#3c4365", width: 1 })
      .move(submenuX, submenuY);

    // Create submenu items
    items.forEach((item, index) => {
      const itemY = submenuY + index * itemHeight + 5;

      // Item background first (so it's behind other elements) - make it fit within submenu borders
      // Use transparent fill by default, only show background on hover
      const itemBg = submenu
        .rect(width - 2, itemHeight - 5)
        .radius(4)
        .fill("transparent")
        .move(submenuX + 1, itemY);

      // Item icon
      const itemIcon = submenu
        .path(item.icon)
        .fill("#555555")
        .size(16, 16)
        .move(submenuX + 15, itemY + 5);

      // Item text
      const itemText = submenu
        .text(item.label)
        .font({ family: "Arial", size: 12 })
        .fill("#000000")
        .move(submenuX + 40, itemY + 5);

      // Function to handle hover effect
      const handleMouseOver = () => {
        itemBg.fill("#f0f0f0");
      };

      const handleMouseLeave = () => {
        itemBg.fill("#ffffff");
      };

      // Function to handle click
      const handleClick = async () => {
        if (item.action === "selectAll") {
          await selectAllElements();
        } else if (item.type) {
          await createNewElementAtPosition(
            originalEvent.offsetX * (draw.viewbox().width / 10000) +
              draw.viewbox().x,
            originalEvent.offsetY * (draw.viewbox().width / 10000) +
              draw.viewbox().y,
            item.type,
          );
        } else if (item.workspaceId) {
          // Handle adding workspace to current workspace
          await addWorkspaceToCurrentWorkspace(item.workspaceId);
        }
        hideContextMenu();
      };

      // Add event listeners to all elements
      itemBg.on("mouseover", handleMouseOver);
      itemBg.on("mouseleave", handleMouseLeave);
      itemBg.on("click", handleClick);

      // Add same event listeners to icon and text
      itemIcon.on("mouseover", handleMouseOver);
      itemIcon.on("mouseleave", handleMouseLeave);
      itemIcon.on("click", handleClick);

      itemText.on("mouseover", handleMouseOver);
      itemText.on("mouseleave", handleMouseLeave);
      itemText.on("click", handleClick);
    });

    return submenu;
  }

  // Add click handler to document to close menu when clicking outside
  documentClickHandler = (evt) => {
    // Check if the click is on the context menu or submenu
    const contextMenuElement = document.getElementById("context-menu");
    const submenuElement = document.getElementById("context-submenu");

    // Don't close if clicking on the menu or submenu
    if (
      (contextMenuElement && contextMenuElement.contains(evt.target)) ||
      (submenuElement && submenuElement.contains(evt.target))
    ) {
      return;
    }

    // Otherwise, hide the menu
    hideContextMenu();
  };

  document.addEventListener("click", documentClickHandler);

  // Prevent default context menu
  e.preventDefault();
};

// Store the document click handler reference
let documentClickHandler = null;

// Helper function to hide context menu
const hideContextMenu = () => {
  // Remove main menu
  const existingMenu = document.getElementById("context-menu");
  if (existingMenu) {
    existingMenu.remove();
  }

  // Remove submenu if exists
  const existingSubmenu = document.getElementById("context-submenu");
  if (existingSubmenu) {
    existingSubmenu.remove();
  }

  // Remove document click listener
  if (documentClickHandler) {
    document.removeEventListener("click", documentClickHandler);
    documentClickHandler = null;
  }
};

export const setupBackgroundListeners = (backgroundRect) => {
  backgroundRect
    .on("dblclick", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (store.state.diagramMode === diagramMode.creationMode) {
        await createNewElementAtPosition(
          e.offsetX * (draw.viewbox().width / 10000) + draw.viewbox().x,
          e.offsetY * (draw.viewbox().width / 10000) + draw.viewbox().y,
        );
      }
    })
    .on("contextmenu", (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        e.preventDefault();
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }

      // Show context menu at mouse position
      const x = e.offsetX * (draw.viewbox().width / 10000) + draw.viewbox().x;
      const y = e.offsetY * (draw.viewbox().width / 10000) + draw.viewbox().y;
      showContextMenu(e, x, y);
    })
    .on("mouseover", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (store.state.dragParams.dragFromElementStarted) {
        const dragParams = store.state.dragParams;
        dragParams.dragDestinationElement = null;
        store.dispatch("updateDragParamsAction", dragParams);
      }
      if (store.state.selectionMode === selectionMode.pointer) {
        backgroundRect.attr({ cursor: "default" });
      } else if (store.state.dragParams.dragFromAnchorStarted) {
        backgroundRect.attr({ cursor: "pointer" });
      } else {
        backgroundRect.attr({ cursor: "grab" });
      }
    })
    .on("mouseup", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (store.state.selectionMode === selectionMode.pointer) {
        backgroundRect.attr({ cursor: "default" });
      } else {
        backgroundRect.attr({ cursor: "grab" });
      }
    })
    .on("mousedown", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      document.activeElement.blur();
      await unselectAllElements();
      await unselectAllSegments();
      if (store.state.selectionMode === selectionMode.pointer) {
        // First we need to make sure that the rectangle selection is not there anymore (weird bug that happened)
        drawAdapter.parameters.rectangleSelection?.entity?.remove();
        drawAdapter.parameters.rectangleSelection.entity = null;

        backgroundRect.attr({ cursor: "default" });
        drawAdapter.parameters.rectangleSelection.entity = draw
          .rect(1, 1)
          .x(
            (e.offsetX + 1) * (draw.viewbox().width / 10000) + draw.viewbox().x,
          ) // adding +1 to avoid facing a weiiiird bug that prevents from double clicking
          .y(
            (e.offsetY + 1) * (draw.viewbox().width / 10000) + draw.viewbox().y,
          ) // adding +1 to avoid facing a weiiiird bug that prevents from double clicking
          .fill("#b9fff5")
          .opacity(0.5);
        drawAdapter.parameters.rectangleSelection.initialX =
          drawAdapter.parameters.rectangleSelection.entity.x();
        drawAdapter.parameters.rectangleSelection.initialY =
          drawAdapter.parameters.rectangleSelection.entity.y();
        backgroundRect.on("mousemove", (e2) => {
          if (store.state.diagramMode === diagramMode.playbackMode) {
            return;
          }
          if (
            store.state.sequences.get(store.state.currentSequenceId) &&
            store.state.sequences.get(store.state.currentSequenceId)
              .currentFrame %
              1 !==
              0
          ) {
            return;
          }
          try {
            drawRectangleSelection(e2);
          } catch (ignored) {
            backgroundRect.off("mousemove");
          }
        });
        backgroundRect.on("mouseup", () => {
          if (store.state.diagramMode === diagramMode.playbackMode) {
            return;
          }
          if (
            store.state.sequences.get(store.state.currentSequenceId) &&
            store.state.sequences.get(store.state.currentSequenceId)
              .currentFrame %
              1 !==
              0
          ) {
            return;
          }
          backgroundRect.off("mousemove");
          if (
            !isEmptyObject(drawAdapter.parameters.rectangleSelection) &&
            drawAdapter.parameters.rectangleSelection.entity
          ) {
            drawAdapter.parameters.rectangleSelection.entity.remove();
            drawAdapter.parameters.rectangleSelection.entity = null;
          }
        });
        drawAdapter.parameters.rectangleSelection.entity
          .on("mousemove", (e) => {
            if (store.state.diagramMode === diagramMode.playbackMode) {
              return;
            }
            if (
              store.state.sequences.get(store.state.currentSequenceId) &&
              store.state.sequences.get(store.state.currentSequenceId)
                .currentFrame %
                1 !==
                0
            ) {
              return;
            }
            drawRectangleSelection(e);
          })
          .on("mouseup", () => {
            if (store.state.diagramMode === diagramMode.playbackMode) {
              return;
            }
            if (
              store.state.sequences.get(store.state.currentSequenceId) &&
              store.state.sequences.get(store.state.currentSequenceId)
                .currentFrame %
                1 !==
                0
            ) {
              return;
            }
            backgroundRect.off("mousemove");
            drawAdapter.parameters.rectangleSelection.entity.remove();
            drawAdapter.parameters.rectangleSelection.entity = null;
            drawAdapter.parameters.rectangleSelection.initialX = null;
            drawAdapter.parameters.rectangleSelection.initialY = null;
            drawAdapter.parameters.rectangleSelection = {};
          });
      } else {
        backgroundRect.attr({ cursor: "grabbing" });
      }
    });
};

export async function resetViewBounds() {
  const viewbox = draw.viewbox();
  if (viewbox.x < 0) {
    await draw
      .animate({ duration: 1, when: "after" })
      .viewbox(
        0,
        Math.max(0, viewbox.y),
        Math.min(viewbox.width, 49999),
        Math.min(viewbox.height, 49999),
      );
  }
  if (viewbox.y < 0) {
    await draw
      .animate({ duration: 1, when: "after" })
      .viewbox(
        Math.max(viewbox.x, 0),
        0,
        Math.min(viewbox.width, 49999),
        Math.min(viewbox.height, 49999),
      );
  }
  if (viewbox.x + (window.innerWidth * viewbox.width) / 10000 > 10000) {
    await draw
      .animate({
        duration: 1,
        when: "after",
      })
      .viewbox(
        10000 - (window.innerWidth * viewbox.width) / 10000,
        Math.max(viewbox.y, 0),
        Math.min(viewbox.width, 49999),
        Math.min(viewbox.height, 49999),
      );
  }
  if (viewbox.y + (window.innerHeight * viewbox.height) / 10000 > 10000) {
    await draw
      .animate({
        duration: 1,
        when: "after",
      })
      .viewbox(
        Math.max(viewbox.x, 0),
        10000 - (window.innerHeight * viewbox.height) / 10000,
        Math.min(viewbox.width, 49999),
        Math.min(viewbox.height, 49999),
      );
  }
}

export const setupSvgContainerListener = (svgContainer) => {
  svgContainer
    .on("zoom", (e) => {
      if (
        store.state.immersiveView &&
        store.state.diagramMode === diagramMode.playbackMode
      ) {
        e.preventDefault();
        return;
      }
      resetViewBounds();
      if (
        store.state.immersiveView &&
        store.state.diagramMode === diagramMode.recordingMode
      ) {
        store.dispatch("toggleImmersiveViewAction", false);
      }
      const viewbox = {
        x: draw.viewbox().x,
        y: draw.viewbox().y,
        width: draw.viewbox().width,
        height: draw.viewbox().height,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
      };
      store.dispatch("setViewboxAction", viewbox);
    })
    .on("panning", () => {
      resetViewBounds();
      if (
        store.state.immersiveView &&
        (store.state.diagramMode === diagramMode.recordingMode ||
          store.state.diagramMode === diagramMode.playbackMode)
      ) {
        store.dispatch("toggleImmersiveViewAction", false);
      }
    })
    .on("panEnd", () => {
      const viewbox = {
        x: draw.viewbox().x,
        y: draw.viewbox().y,
        width: draw.viewbox().width,
        height: draw.viewbox().height,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
      };
      store.dispatch("setViewboxAction", viewbox);
    });
  window.addEventListener("resize", resetViewBounds);
};

async function snapToGridForAllSelectedUsingElementAsReference(element) {
  if (!element) {
    return;
  }
  const positionBeforeSnap = {
    x: element.graphicElement.cx(),
    y: element.graphicElement.cy(),
  };
  snapElementToGrid(element.id);
  if (
    positionBeforeSnap.x !== element.graphicElement.x ||
    positionBeforeSnap.y !== element.graphicElement.y
  ) {
    for (const foundElement of store.getters.currentWorkspaceSelectedElementsIds
      .filter((elementId) => elementId !== element.id)
      .map((elementId) => drawAdapter.parameters.allElements.get(elementId))) {
      if (!foundElement) {
        continue;
      }
      foundElement.graphicElement.dx(
        element.graphicElement.cx() - positionBeforeSnap.x,
      );
      foundElement.graphicElement.dy(
        element.graphicElement.cy() - positionBeforeSnap.y,
      );
      retraceAllSegments(foundElement.id);
      await updateInitialSequencesAfterElementUpdateInCreationMode({
        element: foundElement,
      });
    }
  }
}

export const setupGraphicElementListeners = (element) => {
  const resizer = element.graphicElement.resizer;
  const dummy = element.graphicElement.dummy;
  const toggleDisplayOn = element.graphicElement.toggleDisplayOn;
  const toggleDisplayOff = element.graphicElement.toggleDisplayOff;
  const elementEntity = element.graphicElement.entity;
  element.graphicElement
    .on("dragmove", async (e) => {
      // we can't move elements in playback mode
      if (store.state.diagramMode === diagramMode.playbackMode) {
        e.preventDefault();
        return;
      }
      // we cant move elements if in between frames
      if (
        store.state.diagramMode === diagramMode.recordingMode &&
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        e.preventDefault();
        return;
      }
      // we can't move elements from imported sequences
      if (isElementFromImportedSequence(element.id)) {
        e.preventDefault();
        return;
      }

      drawAdapter.parameters.draggingStarted = true;
      if (store.state.diagramMode === diagramMode.creationMode) {
        await store.dispatch("updateElement", { element: element });
      }
      const storedElement = store.state.allSimpleElements.get(element.id);
      if (
        store.state.diagramMode === diagramMode.recordingMode &&
        storedElement.workspaceParameters[store.state.currentWorkspaceId]
          .destinationElementIds.length > 0
      ) {
        e.preventDefault();
        startDragging(
          element.id,
          e.detail.event.offsetX,
          e.detail.event.offsetY,
        );
      } else {
        // recording mode but isolated elements
        if (store.state.diagramMode === diagramMode.recordingMode) {
          const sequence = store.state.sequences.get(
            store.state.currentSequenceId,
          );
          drawAdapter.parameters.movingObject = true;

          await updateElementPositionWithLazyLoading({
            sequence,
            frameIndex: sequence.currentFrame,
            elementId: element.id,
            elementPosition: getPositionFromGraphicElement(element),
            optionalFunction: showItemPositions,
            undoable: false,
          });

          if (store.state.diagramMode === diagramMode.creationMode) {
            await store.dispatch("updateSimpleElement", {
              simpleElement: storedElement,
              undoable: false,
            });
          }
          showItemPositions(element.id);
        }
        if (store.getters.currentWorkspaceSelectedElementsIds.length > 1) {
          for (const foundElement of store.getters.currentWorkspaceSelectedElementsIds
            .filter((elementId) => elementId !== element.id)
            .map((elementId) =>
              drawAdapter.parameters.allElements.get(elementId),
            )) {
            foundElement.graphicElement.dmove(
              element.graphicElement.x() -
                drawAdapter.parameters.multiObjectsMoveCurrentPosition.x,
              element.graphicElement.y() -
                drawAdapter.parameters.multiObjectsMoveCurrentPosition.y,
            );
            if (store.state.diagramMode === diagramMode.recordingMode) {
              showItemPositions(foundElement.id);
              const sequence = store.state.sequences.get(
                store.state.currentSequenceId,
              );
              await updatePosition({
                sequence,
                frameIndex: sequence.currentFrame,
                elementId: foundElement.id,
                elementPosition: getPositionFromGraphicElement(foundElement),
                type: ELEMENT_POSITION,
                interpolated: false,
                undoable: false,
              });
            }
            await store.dispatch("updateElement", { element: foundElement });
            const storedElement = store.state.allSimpleElements.get(
              foundElement.id,
            );
            await updateInitialSequencesAfterElementUpdateInCreationMode({
              element: storedElement,
            });
          }
        }
        store.getters.currentWorkspaceSelectedElementsIds.forEach((elementId) =>
          retraceAllSegments(elementId),
        );

        drawAdapter.parameters.multiObjectsMoveCurrentPosition.x =
          +element.graphicElement.x();
        drawAdapter.parameters.multiObjectsMoveCurrentPosition.y =
          +element.graphicElement.y();
      }
    })
    .on("dragend", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.diagramMode === diagramMode.recordingMode &&
        store.state.dragParams.dragFromElementStarted
      ) {
        e.preventDefault();
        endDragging(element.id).then();
      } else if (store.state.diagramMode === diagramMode.recordingMode) {
        if (!drawAdapter.parameters.draggingStarted) {
          drawAdapter.parameters.draggingStarted = false;
          drawAdapter.parameters.movingObject = false;
          return;
        }
        for (const elementId of store.getters
          .currentWorkspaceSelectedElementsIds) {
          const storedElement = store.state.allSimpleElements.get(elementId);
          if (
            storedElement.workspaceParameters[store.state.currentWorkspaceId]
              .destinationElementIds.length === 0
          ) {
            const element = drawAdapter.parameters.allElements.get(elementId);
            const sequence = store.state.sequences.get(
              store.state.currentSequenceId,
            );
            await updatePosition({
              sequence,
              frameIndex: sequence.currentFrame,
              elementId: element.id,
              elementPosition: getPositionFromGraphicElement(element),
              type: ELEMENT_POSITION,
              interpolated: false,
              undoable: true,
            });

            blinkValidation(element.id);
            hideItemPositions(element.id);
            showItemPositions(element.id);

            drawAdapter.parameters.draggingStarted = false;
            drawAdapter.parameters.movingObject = false;
          } else {
            // Well... what do we do?
          }
        }
      } else {
        await snapToGridForAllSelectedUsingElementAsReference(element);
        await store.dispatch("updateElement", { element, undoable: true });
        for (const elementId of store.getters
          .currentWorkspaceSelectedElementsIds) {
          const storedElement = store.state.allSimpleElements.get(elementId);
          await updateInitialSequencesAfterElementUpdateInCreationMode({
            element: storedElement,
            undoable: false,
          });
          retraceAllSegments(elementId);
        }
      }
    })
    .on("dblclick", (e) => {
      const storedElement = store.state.allSimpleElements.get(element.id);
      if (store.state.diagramMode === diagramMode.playbackMode) {
        e.preventDefault();
        return;
      }
      if (store.state.diagramMode === diagramMode.recordingMode) {
        e.preventDefault();
      } else {
        if (element.type !== "TEXT-CLASSIC") {
          if (
            !element.graphicElement.elementName ||
            element.graphicElement.elementName.text() === ""
          ) {
            element.graphicElement.elementName = element.graphicElement
              .text(element.name)
              .fill(storedElement.textColor);
            element.graphicElement.elementName
              .amove(
                element.graphicElement.entity.x() +
                  (element.graphicElement.entity.width() -
                    element.graphicElement.elementName.bbox().width) /
                    2,
                element.graphicElement.entity.cy(),
              )
              .attr("pointer-events", "none")
              .attr(
                "style",
                `user-select: none; overflow-wrap: break-word; font-weight: ${storedElement.textWeight}; font-size: ${storedElement.textSize}px; font-family: ${storedElement.textFont}, Helvetica, sans-serif;`,
              );
            element.graphicElement.elementNameMover?.move(
              element.graphicElement.elementName.ax(),
              element.graphicElement.elementName.ay() - 40,
            );
            setupEditableElementNameListeners(
              element.graphicElement.elementName,
              element,
            );
          }
        }
        element.graphicElement.elementName.attr("pointer-events", "all");
        element.graphicElement.elementName.dispatchEvent(new Event("dblclick"));
      }
    })
    .on("mousemove", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (
        !isEmptyObject(drawAdapter.parameters.rectangleSelection) &&
        drawAdapter.parameters.rectangleSelection.entity
      ) {
        await drawRectangleSelection(e);
      }
    })
    .on("mouseup", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        !isEmptyObject(drawAdapter.parameters.rectangleSelection) &&
        drawAdapter.parameters.rectangleSelection.entity
      ) {
        drawAdapter.parameters.rectangleBackground.off("mousemove");
        drawAdapter.parameters.rectangleSelection.entity.remove();
        drawAdapter.parameters.rectangleSelection.entity = null;
        drawAdapter.parameters.rectangleSelection.initialX = null;
        drawAdapter.parameters.rectangleSelection.initialY = null;
        drawAdapter.parameters.rectangleSelection = {};
      }
    })
    .on("mousedown", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      drawAdapter.parameters.multiObjectsMoveInitialPosition.x =
        +element.graphicElement.x();
      drawAdapter.parameters.multiObjectsMoveCurrentPosition.x =
        +element.graphicElement.x();
      drawAdapter.parameters.multiObjectsMoveInitialPosition.y =
        +element.graphicElement.y();
      drawAdapter.parameters.multiObjectsMoveCurrentPosition.y =
        +element.graphicElement.y();
    })
    .on("mouseover", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      // Don't allow hovering on elements from imported sequences
      if (isElementFromImportedSequence(element.id)) {
        return;
      }
      const dragParams = store.state.dragParams;
      highlightElement(element.id);
      if (store.state.diagramMode === diagramMode.recordingMode) {
        showItemPositions(element.id);
        elementEntity.attr({ cursor: "cell" });
        const simpleElement = store.state.allSimpleElements.get(element.id);
        if (store.state.dragParams.dragFromElementStarted) {
          if (
            simpleElement.workspaceParameters[
              store.state.currentWorkspaceId
            ].destinationElementIds.includes(
              store.state.dragParams.dragSourceElement,
            )
          ) {
            element.graphicElement.entity.stroke({
              color: "#00ff20",
              width: 4,
            });
            const dragParams = store.state.dragParams;
            dragParams.dragDestinationElement = element.id;
            store.dispatch("updateDragParamsAction", dragParams);
          } else {
            if (element.id !== store.state.dragParams.dragSourceElement) {
              element.graphicElement.entity.stroke({
                color: "#ff0000",
                width: 4,
              });
            }
            elementEntity.attr({ cursor: "not-allowed" });
          }
        } else if (
          simpleElement.workspaceParameters[store.state.currentWorkspaceId]
            .destinationElementIds.length === 0
        ) {
          elementEntity.attr({ cursor: "move" });
          if (store.state.diagramMode === diagramMode.recordingMode) {
            showItemPositions(element.id);
          }
          highlightElement(element.id);
        }
      } else if (dragParams.dragFromAnchorStarted) {
        dragParams.dragDestinationElement = element.id;
        if (dragParams.dragFromAnchorElementId !== element.id) {
          elementEntity.attr({ cursor: "pointer" });
          element.graphicElement.entity.stroke({
            color: "#00ff20",
            width: 4,
          });
          dragParams.isHoveringAnotherElement = true;
        } else {
          elementEntity.attr({ cursor: "not-allowed" });
        }
      } else {
        elementEntity.attr({ cursor: "move" });
      }
      element.graphicElement.resizer.opacity(1);
      store.dispatch("updateDragParamsAction", dragParams).then();
    })
    .on("mouseleave", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (
        !store.getters.currentWorkspaceSelectedElementsIds.includes(element.id)
      ) {
        hideItemPositions(element.id);
        unlightElement(element.id);
      }
      const dragParams = store.state.dragParams;
      dragParams.dragDestinationElement = null;
      dragParams.isHoveringAnotherElement = false;
      store.dispatch("updateDragParamsAction", dragParams).then();

      // Delete element icons
      element.graphicElement.actionMenuBackground?.remove();
      element.graphicElement.deleteBtn?.remove();
      element.graphicElement.workspaceBtn?.remove();
      element.graphicElement.renameBtn?.remove();
      element.graphicElement.colorBtn?.remove();
      element.graphicElement.textBtn?.remove();
      element.graphicElement.expandCollapseBtn?.remove();
      element.graphicElement.actionMenuBackground = null;
      element.graphicElement.deleteBtn = null;
      element.graphicElement.workspaceBtn = null;
      element.graphicElement.deleteBtn = null;
      element.graphicElement.renameBtn = null;
      element.graphicElement.colorBtn = null;
      element.graphicElement.expandCollapseBtn = null;
    });

  toggleDisplayOn
    .on("mouseover", () => {
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      toggleDisplayOn.stroke({ color: "#000", width: 1.2 });
    })
    .on("mouseleave", () => {
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      toggleDisplayOn.stroke({ color: "#000", width: 1 });
    })
    .on("dragmove", (e) => {
      e.preventDefault();
    })
    .on("click", async (e) => {
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        e.preventDefault();
        return;
      }
      let sequence = store.state.sequences.get(store.state.currentSequenceId);
      let frameIndex = sequence.currentFrame;
      await updateElementOpacity(sequence, frameIndex, element.id, 1);
      const recording = store.state.diagramMode === diagramMode.recordingMode;
      const hidden = recording ? 0.3 : 0;
      const frameTicksElapsed = frameIndex % 1;
      setElementOpacity(
        element,
        sequence,
        Math.round(frameIndex),
        hidden,
        frameTicksElapsed,
      );
      setDisplayToggleFromCurrentVisibility(sequence, element.id);
    });

  toggleDisplayOff
    .on("mouseover", () => {
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      toggleDisplayOff.stroke({ color: "#000", width: 1.2 });
    })
    .on("mouseleave", () => {
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      toggleDisplayOff.stroke({ color: "#000", width: 1 });
    })
    .on("dragmove", (e) => {
      e.preventDefault();
    })
    .on("click", async (e) => {
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        e.preventDefault();
        return;
      }
      let sequence = store.state.sequences.get(store.state.currentSequenceId);
      let frameIndex = sequence.currentFrame;
      await updateElementOpacity(
        sequence,
        sequence.currentFrame,
        element.id,
        0,
      );
      const recording = store.state.diagramMode === diagramMode.recordingMode;
      const hidden = recording ? 0.3 : 0;
      const frameTicksElapsed = frameIndex % 1;
      setElementOpacity(
        element,
        sequence,
        Math.round(frameIndex),
        hidden,
        frameTicksElapsed,
      );
      setDisplayToggleFromCurrentVisibility(sequence, element.id);
    });

  resizer
    .on("dragmove", async (e) => {
      if (element.type === "TEXT-CLASSIC") {
        e.preventDefault();
      }
      if (store.state.diagramMode === diagramMode.playbackMode) {
        e.preventDefault();
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        e.preventDefault();
        return;
      }
      let newWidth = e.detail.box.x2 - element.graphicElement.entity.x();
      let newHeight = e.detail.box.y2 - element.graphicElement.entity.y();
      const originalX = element.graphicElement.entity.x();
      const originalY = element.graphicElement.entity.y();
      element.graphicElement.resizer.opacity(1);
      if (newWidth <= 10 || newHeight <= 10) {
        element.graphicElement.resizer.opacity(0);
        return;
      }
      element.graphicElement.entity
        .width(newWidth)
        .height(newHeight)
        .move(originalX, originalY);
      element.graphicElement.dummy
        .width(newWidth)
        .height(newHeight)
        .center(
          element.graphicElement.entity.cx(),
          element.graphicElement.entity.cy(),
        );
      element.graphicElement.dummyStroke
        .width(newWidth)
        .height(newHeight)
        .center(
          element.graphicElement.entity.cx(),
          element.graphicElement.entity.cy(),
        );
      element.graphicElement.elementName.width(
        element.graphicElement.entity.width() - 10,
      );
      // we update the expand button position
      element.graphicElement.workspaceExpandBtnGroup?.center(
        element.graphicElement.entity.x() +
          element.graphicElement.entity.width() -
          10,
        element.graphicElement.entity.y() - 14,
      );

      if (element.type === "TEXT-CLASSIC") {
        const editableDiv = element.graphicElement.elementName.editableDiv;
        element.graphicElement.elementName.x(
          element.graphicElement.entity.x() + 5,
        );
        element.graphicElement.elementName.y(
          element.graphicElement.entity.y() + 5,
        );
        editableDiv.style.maxWidth = `${element.graphicElement.entity.width() - 10}px`;

        element.graphicElement.elementName.height(editableDiv.offsetHeight);

        element.graphicElement.entity.height(
          element.graphicElement.elementName.height() + 5,
        );
        element.graphicElement.dummy.height(
          element.graphicElement.elementName.height() + 5,
        );
        element.graphicElement.dummyStroke.height(
          element.graphicElement.elementName.height() + 5,
        );

        element.graphicElement.resizer.move(
          element.graphicElement.entity.x() + elementEntity.width() - 15,
          element.graphicElement.entity.y() + elementEntity.height() - 15,
        );
      }
      const simpleElement = store.state.allSimpleElements.get(element.id);

      // Update element dimensions and relative anchor positions
      const workspaceParams =
        simpleElement.workspaceParameters[store.state.currentWorkspaceId];
      workspaceParams.width = newWidth;
      workspaceParams.height = newHeight;
      workspaceParams.anchors = createAnchors(
        workspaceParams.x,
        workspaceParams.y,
        newWidth,
        newHeight,
      );

      if (
        workspaceParams.destinationElementIds.length > 0 ||
        store.state.diagramMode === diagramMode.creationMode
      ) {
        configureElementAnchors(element.id);
        retraceAllSegments(element.id);
      }

      if (store.state.diagramMode === "RECORDING_MODE") {
        let elementCx = element.graphicElement.entity.cx();
        let elementCy = element.graphicElement.entity.cy();
        element.graphicElement.toggleDisplayOn
          .x(elementCx - element.graphicElement.entity.width() / 4)
          .y(elementCy + element.graphicElement.entity.height() / 2);
        element.graphicElement.toggleDisplayOff
          .x(elementCx + element.graphicElement.entity.width() / 4)
          .y(elementCy + element.graphicElement.entity.height() / 2 + 5);
      }

      await store.dispatch("updateElement", { element });
      const storedElement = store.state.allSimpleElements.get(element.id);
      await updateInitialSequencesAfterElementUpdateInCreationMode({
        element: storedElement,
      });
    })
    .on("dragend", async () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      element.graphicElement.entity
        .width(Math.round(element.graphicElement.entity.width() / 10) * 10)
        .height(Math.round(element.graphicElement.entity.height() / 10) * 10);
      element.graphicElement.dummy
        .width(Math.round(element.graphicElement.entity.width() / 10) * 10)
        .height(Math.round(element.graphicElement.entity.height() / 10) * 10)
        .center(
          element.graphicElement.entity.cx(),
          element.graphicElement.entity.cy(),
        );
      element.graphicElement.dummyStroke
        .width(Math.round(element.graphicElement.entity.width() / 10) * 10)
        .height(Math.round(element.graphicElement.entity.height() / 10) * 10)
        .center(
          element.graphicElement.entity.cx(),
          element.graphicElement.entity.cy(),
        );
      element.graphicElement.elementName.width(
        Math.round(element.graphicElement.entity.width() / 10) * 10,
      );
      const elementEntityX = elementEntity.x();
      const elementEntityY = elementEntity.y();
      element.graphicElement.resizer.move(
        elementEntityX + elementEntity.width() - 15,
        elementEntityY + elementEntity.height() - 15,
      );
      const storedElement = store.state.allSimpleElements.get(element.id);

      // Update element dimensions and relative anchor positions for final resize
      const finalWorkspaceParams =
        storedElement.workspaceParameters[store.state.currentWorkspaceId];
      const finalWidth = Math.round(elementEntity.width() / 10) * 10;
      const finalHeight = Math.round(elementEntity.height() / 10) * 10;
      finalWorkspaceParams.width = finalWidth;
      finalWorkspaceParams.height = finalHeight;
      finalWorkspaceParams.anchors = createAnchors(
        finalWorkspaceParams.x,
        finalWorkspaceParams.y,
        finalWidth,
        finalHeight,
      );

      await store.dispatch("updateElement", { element, undoable: true });
      await snapToGridForAllSelectedUsingElementAsReference(element);
      await updateInitialSequencesAfterElementUpdateInCreationMode({
        element: storedElement,
        undoable: true,
      });
      const simpleElement = store.state.allSimpleElements.get(element.id);
      if (
        simpleElement.workspaceParameters[store.state.currentWorkspaceId]
          .destinationElementIds.length > 0 ||
        store.state.diagramMode === diagramMode.creationMode
      ) {
        configureElementAnchors(element.id);
        retraceAllSegments(element.id);
      }
    })
    .on("mouseover", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      element.graphicElement.resizer.attr({ cursor: "se-resize" });
      if (store.state.diagramMode === diagramMode.recordingMode) {
        showItemPositions(element.id);
      }
      highlightElement(element.id);
      // element.graphicElement.resizer.opacity(1)
    })
    .on("mouseleave", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (
        !store.getters.currentWorkspaceSelectedElementsIds.includes(element.id)
      ) {
        unlightElement(element.id);
      }
    });
  dummy
    .on("mouseover", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      const dragParams = store.state.dragParams;
      highlightElement(element.id);
      if (store.state.diagramMode === diagramMode.recordingMode) {
        showItemPositions(element.id);
        elementEntity.attr({ cursor: "cell" });
        dummy.attr({ cursor: "cell" });
        const simpleElement = store.state.allSimpleElements.get(element.id);
        if (store.state.dragParams.dragFromElementStarted) {
          if (
            simpleElement.workspaceParameters[
              store.state.currentWorkspaceId
            ].destinationElementIds.includes(
              store.state.dragParams.dragSourceElement,
            )
          ) {
            element.graphicElement.entity.stroke({
              color: "#00ff20",
              width: 4,
            });
            store.state.dragParams.dragDestinationElement = element;
          } else {
            if (element.id !== store.state.dragParams.dragSourceElement) {
              element.graphicElement.entity.stroke({
                color: "#ff0000",
                width: 4,
              });
            }
            elementEntity.attr({ cursor: "not-allowed" });
            dummy.attr({ cursor: "not-allowed" });
          }
        } else if (
          simpleElement.workspaceParameters[store.state.currentWorkspaceId]
            .destinationElementIds.length === 0
        ) {
          elementEntity.attr({ cursor: "move" });
          dummy.attr({ cursor: "move" });
          if (store.state.diagramMode === diagramMode.recordingMode) {
            showItemPositions(element.id);
          }
          highlightElement(element.id);
        }
      } else if (dragParams.dragFromAnchorStarted) {
        dragParams.dragDestinationElement = element.id;
        if (dragParams.dragFromAnchorElementId !== element.id) {
          elementEntity.attr({ cursor: "pointer" });
          dummy.attr({ cursor: "pointer" });
        } else {
          elementEntity.attr({ cursor: "not-allowed" });
          dummy.attr({ cursor: "not-allowed" });
        }
      } else {
        elementEntity.attr({ cursor: "move" });
        dummy.attr({ cursor: "move" });
      }
      store.dispatch("updateDragParamsAction", dragParams).then();
    })
    .on("mouseleave", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      const dragParams = store.state.dragParams;
      dragParams.dragDestinationElement = null;
      store.dispatch("updateDragParamsAction", dragParams).then();
    })
    .on("click", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (e.ctrlKey || e.metaKey) {
        if (
          store.getters.currentWorkspaceSelectedElementsIds.includes(element.id)
        ) {
          removeElementFromSelection(element.id);
        } else {
          await addElementToSelection({
            elementId: element.id,
            deselectOthers: false,
            undoable: false,
          });
        }
      } else {
        if (!drawAdapter.parameters.draggingStarted) {
          await addElementToSelection({
            elementId: element.id,
            deselectOthers: true,
            undoable: false,
          });
          await unselectAllSegments();
        }
      }
      drawAdapter.parameters.multiObjectsMoveInitialPosition = {};
      drawAdapter.parameters.multiObjectsMoveCurrentPosition = {};
      drawAdapter.parameters.draggingStarted = false;
    })
    .on("mousedown", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      document.activeElement.blur();
      if (
        !store.getters.currentWorkspaceSelectedElementsIds.includes(
          element.id,
        ) &&
        !e.ctrlKey &&
        !e.metaKey
      ) {
        await addElementToSelection({
          elementId: element.id,
          deselectOthers: true,
          undoable: false,
        });
        await unselectAllSegments();
      }
    });
};

// Helper function to show element context menu
const showElementContextMenu = (e, element) => {
  // Remove any existing context menu
  hideContextMenu();

  // Get element position
  const x = element.graphicElement.entity.cx();
  const y = element.graphicElement.entity.cy();

  // Check if multiple elements are selected
  const selectedElementsIds = store.getters.currentWorkspaceSelectedElementsIds;
  const isMultipleSelection = selectedElementsIds.length > 1;

  // Menu dimensions
  const menuWidth = 180;
  const submenuWidth = 150;
  const itemHeight = 30;

  // Get element type for shape menu
  const simpleElement = store.state.allSimpleElements.get(element.id);
  const hasChildWorkspace = simpleElement && simpleElement.childWorkspaceId;

  // Get all available workspaces that the element can be moved to
  const availableWorkspaces = store.getters.workspaces.filter((workspace) => {
    // Don't include current workspace or the element's child workspace
    // Include root workspace only if we're not already in it
    return (
      workspace.id !== store.state.currentWorkspaceId &&
      (workspace.id !== store.state.rootWorkspaceId ||
        store.state.currentWorkspaceId !== store.state.rootWorkspaceId) &&
      workspace.id !== simpleElement.childWorkspaceId
    );
  });

  // Create menu sections based on whether multiple elements are selected
  let sections = [];

  if (isMultipleSelection) {
    // Menu for multiple selected elements - exclude workspace and rename options
    sections = [
      {
        title: "Change Color",
        icon: "M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .***********.*********** 1.06.64 1.67 0 1.38-1.12 2.5-2.5 2.5zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5 0-.16-.08-.28-.14-.35-.41-.46-.63-1.05-.63-1.65 0-1.38 1.12-2.5 2.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z", // Color icon
        action: "multiColor",
        hasSubmenu: false,
      },
      {
        title: "Move to Workspace",
        icon: "M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10z", // Folder icon
        items: [
          {
            label: "New Workspace",
            action: "moveMultipleToNewWorkspace",
            icon: "M20 6h-8l-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-4 10h-3v3h-2v-3H8v-2h3V11h2v3h3v2z",
          },
          ...availableWorkspaces.map((workspace) => ({
            label: workspace.name,
            workspaceId: workspace.id,
            icon: "M20 6h-8l-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2z",
          })),
        ],
        hasSubmenu: true,
      },
      {
        title: "Text Properties",
        icon: "M5 4v3h5.5v12h3V7H19V4H5z", // Text icon
        action: "multiTextProperties",
        hasSubmenu: false,
      },
      {
        title: "Delete All",
        icon: "M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z", // Delete icon
        action: "multiDelete",
        hasSubmenu: false,
      },
    ];
  } else {
    // Regular menu for single element
    sections = [
      {
        title: "Change Shape",
        icon: "M3 3h18v18H3V3z", // Shape icon
        items: [
          { label: "Rectangle", type: "SQUARE", icon: "M3 3h18v18H3V3z" },
          {
            label: "Circle",
            type: "CIRCLE",
            icon: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z",
          },
          {
            label: "Diamond",
            type: "RHOMBUS",
            icon: "M12 2L2 12l10 10 10-10L12 2z",
          },
          {
            label: "Text",
            type: "TEXT-CLASSIC",
            icon: "M5 4v3h5.5v12h3V7H19V4H5z",
          },
        ],
        hasSubmenu: true,
      },
      {
        title: "Change Color",
        icon: "M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .***********.*********** 1.06.64 1.67 0 1.38-1.12 2.5-2.5 2.5zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5 0-.16-.08-.28-.14-.35-.41-.46-.63-1.05-.63-1.65 0-1.38 1.12-2.5 2.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z", // Color icon
        action: "color",
        hasSubmenu: false,
      },
      {
        title: "Rename",
        icon: "M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z", // Rename icon
        action: "rename",
        hasSubmenu: false,
      },
      {
        title: hasChildWorkspace ? "Enter Workspace" : "Create Workspace",
        icon: "M19 11h-4v4h-2v-4H9v-2h4V5h2v4h4v2z", // Workspace icon
        action: "workspace",
        hasSubmenu: false,
      },
      {
        title: hasChildWorkspace
          ? isWorkspaceExpanded(element.id)
            ? "Collapse Workspace"
            : "Expand Workspace"
          : "",
        icon: isWorkspaceExpanded(element.id)
          ? "M19 13H5v-2h14v2z" // Minus icon for collapse
          : "M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7z", // Plus icon for expand
        action: "expandCollapseWorkspace",
        hasSubmenu: false,
        hidden: !hasChildWorkspace,
      },
      {
        title: "Move to Workspace",
        icon: "M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10z", // Folder icon
        items: [
          {
            label: "New Workspace",
            action: "moveToNewWorkspace",
            icon: "M20 6h-8l-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-4 10h-3v3h-2v-3H8v-2h3V11h2v3h3v2z",
          },
          ...availableWorkspaces.map((workspace) => ({
            label: workspace.name,
            workspaceId: workspace.id,
            icon: "M20 6h-8l-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2z",
          })),
        ],
        hasSubmenu: true,
      },
      {
        title: "Text Properties",
        icon: "M5 4v3h5.5v12h3V7H19V4H5z", // Text icon
        action: "textProperties",
        hasSubmenu: false,
      },
      {
        title: "Delete",
        icon: "M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z", // Delete icon
        action: "delete",
        hasSubmenu: false,
      },
    ];
  }

  // Filter out hidden sections and calculate menu height
  const visibleSections = sections.filter((section) => !section.hidden);
  const titleHeight = 40; // Title + separator
  const paddingBottom = 10;
  const menuHeight =
    titleHeight + visibleSections.length * (itemHeight + 10) + paddingBottom;

  // Create context menu container
  const contextMenu = draw.group().attr("id", "context-menu");

  // Create background for the menu first (so it's behind other elements)
  contextMenu
    .rect(menuWidth, menuHeight)
    .radius(8)
    .fill("#ffffff")
    .opacity(0.95)
    .stroke({ color: "#3c4365", width: 1 })
    .move(x, y - menuHeight / 2);

  // Create menu title
  contextMenu
    .text(isMultipleSelection ? "Multiple Elements" : "Element Menu")
    .font({ family: "Arial", size: 14, weight: "bold" })
    .fill("#3c4365")
    .move(x + 10, y - menuHeight / 2 + 10);

  // Add separator line
  contextMenu
    .line(
      x + 10,
      y - menuHeight / 2 + 30,
      x + menuWidth - 10,
      y - menuHeight / 2 + 30,
    )
    .stroke({ color: "#e0e0e0", width: 1 });

  // Track active submenu
  let activeSubmenu = null;
  let activeSection = null;

  // Create menu sections
  let currentY = y - menuHeight / 2 + 40;

  // Store menu item positions for each section
  const sectionPositions = [];

  sections.forEach((section, sectionIndex) => {
    // Skip hidden sections
    if (section.hidden) return;

    // Store the position of this section
    sectionPositions[sectionIndex] = { x: x + 10, y: currentY - 5 };

    // Create section background for hover effect first (so it's behind other elements)
    const sectionBg = contextMenu
      .rect(menuWidth - 2, itemHeight)
      .radius(4)
      .fill("transparent")
      .move(x + 1, sectionPositions[sectionIndex].y);

    // Section title
    const sectionTitle = contextMenu
      .text(section.title)
      .font({ family: "Arial", size: 12, weight: "bold" })
      .fill("#555555")
      .move(
        sectionPositions[sectionIndex].x + 25,
        sectionPositions[sectionIndex].y + 5,
      );

    // Section icon
    const sectionIcon = contextMenu
      .path(section.icon)
      .fill("#555555")
      .size(16, 16)
      .move(
        sectionPositions[sectionIndex].x + 2,
        sectionPositions[sectionIndex].y + 5,
      );

    // Add arrow icon if has submenu
    let arrowIcon = null;
    if (section.hasSubmenu) {
      arrowIcon = contextMenu
        .path("M10 17l5-5-5-5v10z") // Right arrow icon
        .fill("#555555")
        .size(12, 12)
        .move(
          sectionPositions[sectionIndex].x + menuWidth - 35,
          sectionPositions[sectionIndex].y + 5,
        );
    }

    // Function to show submenu
    const showSubmenu = () => {
      sectionBg.fill("#f0f0f0");

      // Hide previous submenu if different section
      if (activeSubmenu && activeSection !== sectionIndex) {
        activeSubmenu.remove();
        activeSubmenu = null;
      }

      // Show submenu if this section has one
      if (section.hasSubmenu && !activeSubmenu) {
        activeSection = sectionIndex;
        // Position submenu to the right of the menu item, aligned with its top
        // Use the exact position of the menu item from sectionPositions
        activeSubmenu = createSubmenu(
          section.items,
          sectionPositions[sectionIndex].x + menuWidth - 10,
          sectionPositions[sectionIndex].y,
          submenuWidth,
          e,
          element,
        );
      }
    };

    // Function to handle mouse leave
    const handleMouseLeave = (evt) => {
      sectionBg.fill("transparent");

      // Check if mouse moved to submenu
      if (activeSubmenu) {
        // Get submenu position - must match the calculation in showSubmenu
        const submenuX = sectionPositions[sectionIndex].x + menuWidth - 10;
        const submenuY = sectionPositions[sectionIndex].y;
        const submenuHeight = section.items
          ? section.items.length * itemHeight + 10
          : 0;

        // Only hide submenu if mouse didn't move to submenu area
        if (
          evt.clientX < submenuX ||
          evt.clientX > submenuX + submenuWidth ||
          evt.clientY < submenuY ||
          evt.clientY > submenuY + submenuHeight
        ) {
          setTimeout(() => {
            // Additional check to prevent flickering
            if (
              activeSubmenu &&
              !activeSubmenu.node.matches(":hover") &&
              !sectionBg.node.matches(":hover") &&
              !sectionTitle.node.matches(":hover") &&
              !sectionIcon.node.matches(":hover") &&
              (!arrowIcon || !arrowIcon.node.matches(":hover"))
            ) {
              activeSubmenu.remove();
              activeSubmenu = null;
            }
          }, 100);
        }
      }
    };

    // Add event listeners to all elements
    sectionBg.on("mouseover", showSubmenu);
    sectionBg.on("mouseleave", handleMouseLeave);

    // Add same event listeners to text and icon
    sectionTitle.on("mouseover", showSubmenu);
    sectionTitle.on("mouseleave", handleMouseLeave);

    sectionIcon.on("mouseover", showSubmenu);
    sectionIcon.on("mouseleave", handleMouseLeave);

    if (arrowIcon) {
      arrowIcon.on("mouseover", showSubmenu);
      arrowIcon.on("mouseleave", handleMouseLeave);
    }

    // Add click handler for non-submenu sections
    if (!section.hasSubmenu) {
      // Function to handle click on action items
      const handleActionClick = async () => {
        // Handle multi-element actions
        if (section.action === "multiDelete") {
          await deleteSelectedElements({
            force: false,
            undoable: true,
          });
          hideContextMenu();
        } else if (section.action === "multiColor") {
          // Create color picker for multiple elements
          const colorPicker = document.createElement("input");
          colorPicker.type = "color";
          colorPicker.value =
            element.graphicElement.entity.attr("fill") ||
            simpleElement.color ||
            "#ffffff";

          // Add event listener to update all selected elements' colors
          colorPicker.addEventListener("input", async (e) => {
            const newColor = e.target.value;
            await updateColorForSelectedElements(newColor);
          });

          // Trigger color picker dialog
          colorPicker.click();
          hideContextMenu();
        } else if (section.action === "multiTextProperties") {
          // Create text properties popup for multiple elements
          const popup = document.createElement("div");
          popup.style.position = "fixed";
          popup.style.top = "50%";
          popup.style.left = "50%";
          popup.style.transform = "translate(-50%, -50%)";
          popup.style.backgroundColor = "#2a2d32ff";
          popup.style.padding = "20px";
          popup.style.borderRadius = "8px";
          popup.style.boxShadow = "0 0 10px rgba(0,0,0,0.5)";
          popup.style.zIndex = "1000";
          popup.style.width = "250px";
          popup.style.color = "white";
          popup.innerHTML =
            "<div style='text-align: center; margin-bottom: 15px; font-weight: bold;'>Update Multiple Elements</div>";

          // Font Size
          const fontSizeLabel = document.createElement("div");
          fontSizeLabel.style.color = "white";
          fontSizeLabel.style.marginBottom = "10px";
          fontSizeLabel.textContent = "Font Size";
          popup.appendChild(fontSizeLabel);

          const fontSizeSelect = document.createElement("select");
          fontSizeSelect.style.width = "100%";
          fontSizeSelect.style.marginBottom = "15px";
          fontSizeSelect.style.padding = "8px";
          fontSizeSelect.style.backgroundColor = "#333740";
          fontSizeSelect.style.color = "white";
          fontSizeSelect.style.border = "none";
          fontSizeSelect.style.borderRadius = "4px";

          [
            2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 40, 44, 48, 52,
            56, 60, 64, 72, 80,
          ].forEach((size) => {
            const option = document.createElement("option");
            option.value = size;
            option.textContent = size + "px";
            fontSizeSelect.appendChild(option);
          });
          popup.appendChild(fontSizeSelect);

          // Font Family
          const fontFamilyLabel = document.createElement("div");
          fontFamilyLabel.style.color = "white";
          fontFamilyLabel.style.marginBottom = "10px";
          fontFamilyLabel.textContent = "Font Family";
          popup.appendChild(fontFamilyLabel);

          const fontFamilySelect = document.createElement("select");
          fontFamilySelect.style.width = "100%";
          fontFamilySelect.style.marginBottom = "15px";
          fontFamilySelect.style.padding = "8px";
          fontFamilySelect.style.backgroundColor = "#333740";
          fontFamilySelect.style.color = "white";
          fontFamilySelect.style.border = "none";
          fontFamilySelect.style.borderRadius = "4px";

          const fontFamilies = [
            { name: "Arial", value: "Arial, sans-serif" },
            { name: "Courier New", value: '"Courier New", Courier, monospace' },
            {
              name: "Times New Roman",
              value: '"Times New Roman", Times, serif',
            },
            { name: "Georgia", value: "Georgia, serif" },
            { name: "Verdana", value: "Verdana, sans-serif" },
          ];

          fontFamilies.forEach((font) => {
            const option = document.createElement("option");
            option.value = font.name;
            option.textContent = font.name;
            fontFamilySelect.appendChild(option);
          });
          popup.appendChild(fontFamilySelect);

          // Font Weight
          const fontWeightLabel = document.createElement("div");
          fontWeightLabel.style.color = "white";
          fontWeightLabel.style.marginBottom = "10px";
          fontWeightLabel.textContent = "Font Weight";
          popup.appendChild(fontWeightLabel);

          const fontWeightSelect = document.createElement("select");
          fontWeightSelect.style.width = "100%";
          fontWeightSelect.style.marginBottom = "15px";
          fontWeightSelect.style.padding = "8px";
          fontWeightSelect.style.backgroundColor = "#333740";
          fontWeightSelect.style.color = "white";
          fontWeightSelect.style.border = "none";
          fontWeightSelect.style.borderRadius = "4px";

          ["normal", "bold", "lighter", "bolder"].forEach((weight) => {
            const option = document.createElement("option");
            option.value = weight;
            option.textContent = weight;
            fontWeightSelect.appendChild(option);
          });
          popup.appendChild(fontWeightSelect);

          // Text Align
          const textAlignLabel = document.createElement("div");
          textAlignLabel.style.color = "white";
          textAlignLabel.style.marginBottom = "10px";
          textAlignLabel.textContent = "Text Align";
          popup.appendChild(textAlignLabel);

          const textAlignSelect = document.createElement("select");
          textAlignSelect.style.width = "100%";
          textAlignSelect.style.marginBottom = "15px";
          textAlignSelect.style.padding = "8px";
          textAlignSelect.style.backgroundColor = "#333740";
          textAlignSelect.style.color = "white";
          textAlignSelect.style.border = "none";
          textAlignSelect.style.borderRadius = "4px";

          ["left", "center", "right"].forEach((align) => {
            const option = document.createElement("option");
            option.value = align;
            option.textContent = align;
            textAlignSelect.appendChild(option);
          });
          popup.appendChild(textAlignSelect);

          // Text Color
          const textColorLabel = document.createElement("div");
          textColorLabel.style.color = "white";
          textColorLabel.style.marginBottom = "10px";
          textColorLabel.textContent = "Text Color";
          popup.appendChild(textColorLabel);

          const textColorPicker = document.createElement("input");
          textColorPicker.type = "color";
          textColorPicker.value = "#ffffff";
          textColorPicker.style.width = "100%";
          textColorPicker.style.height = "30px";
          textColorPicker.style.marginBottom = "15px";
          textColorPicker.style.padding = "0";
          textColorPicker.style.border = "none";
          textColorPicker.style.borderRadius = "4px";
          popup.appendChild(textColorPicker);

          // Apply and Close buttons
          const buttonContainer = document.createElement("div");
          buttonContainer.style.display = "flex";
          buttonContainer.style.justifyContent = "space-between";
          buttonContainer.style.marginTop = "15px";

          const applyButton = document.createElement("button");
          applyButton.textContent = "Apply";
          applyButton.style.backgroundColor = "#4CAF50";
          applyButton.style.color = "white";
          applyButton.style.border = "none";
          applyButton.style.padding = "8px 16px";
          applyButton.style.borderRadius = "4px";
          applyButton.style.cursor = "pointer";

          const closeButton = document.createElement("button");
          closeButton.textContent = "Close";
          closeButton.style.backgroundColor = "#f44336";
          closeButton.style.color = "white";
          closeButton.style.border = "none";
          closeButton.style.padding = "8px 16px";
          closeButton.style.borderRadius = "4px";
          closeButton.style.cursor = "pointer";

          buttonContainer.appendChild(applyButton);
          buttonContainer.appendChild(closeButton);
          popup.appendChild(buttonContainer);

          // Add event listeners
          applyButton.addEventListener("click", async () => {
            await updateTextPropertiesForSelectedElements({
              textColor: textColorPicker.value,
              textSize: parseInt(fontSizeSelect.value),
              textFont: fontFamilySelect.value,
              textWeight: fontWeightSelect.value,
              textAlign: textAlignSelect.value,
            });
            document.body.removeChild(popup);
            hideContextMenu();
          });

          closeButton.addEventListener("click", () => {
            document.body.removeChild(popup);
            hideContextMenu();
          });

          document.body.appendChild(popup);
          hideContextMenu();

          // Handle single element actions
        } else if (section.action === "delete") {
          await deleteElement({
            elementId: element.id,
            force: false,
            undoable: true,
          });
          hideContextMenu();
        } else if (section.action === "rename") {
          element.graphicElement.elementName.dispatchEvent(
            new Event("dblclick"),
          );
          hideContextMenu();
        } else if (section.action === "workspace") {
          if (hasChildWorkspace) {
            await enterWorkspaceWithZoom(
              simpleElement.childWorkspaceId,
              element.id,
            );
          } else {
            await createWorkspace(element.id);
          }
          hideContextMenu();
        } else if (section.action === "expandCollapseWorkspace") {
          if (hasChildWorkspace) {
            if (isWorkspaceExpanded(element.id)) {
              await collapseWorkspace(element.id);
            } else {
              await expandWorkspace({
                elementId: element.id,
                pushElements: true,
                undoable: true,
              });
            }
          }
          hideContextMenu();
        } else if (section.action === "color") {
          // Create color picker
          const colorPicker = document.createElement("input");
          colorPicker.type = "color";
          colorPicker.value =
            element.graphicElement.entity.attr("fill") ||
            simpleElement.color ||
            "#ffffff";

          // Add event listener to update element color
          colorPicker.addEventListener("input", async (e) => {
            const newColor = e.target.value;
            // Update element color
            element.graphicElement.entity.fill(newColor);

            // Update element in store
            const updatedElement = deepClone(simpleElement);
            updatedElement.color = newColor;
            await store.dispatch("updateSimpleElement", {
              simpleElement: updatedElement,
              undoable: true,
            });
            updateGraphicElementFromState(element.id);
          });

          // Trigger color picker dialog
          colorPicker.click();
          hideContextMenu();
        } else if (section.action === "textProperties") {
          // Create text properties popup using the same approach as the expanded menu
          const popup = document.createElement("div");
          popup.style.position = "fixed";
          popup.style.top = "50%";
          popup.style.left = "50%";
          popup.style.transform = "translate(-50%, -50%)";
          popup.style.backgroundColor = "#2a2d32ff";
          popup.style.padding = "20px";
          popup.style.borderRadius = "8px";
          popup.style.boxShadow = "0 0 10px rgba(0,0,0,0.5)";
          popup.style.zIndex = "1000";
          popup.style.width = "250px";

          // Font Size
          const fontSizeLabel = document.createElement("div");
          fontSizeLabel.style.color = "white";
          fontSizeLabel.style.marginBottom = "10px";
          fontSizeLabel.textContent = "Font Size";
          popup.appendChild(fontSizeLabel);

          const fontSizeSelect = document.createElement("select");
          fontSizeSelect.style.width = "100%";
          fontSizeSelect.style.marginBottom = "15px";
          fontSizeSelect.style.padding = "8px";
          fontSizeSelect.style.backgroundColor = "#333740";
          fontSizeSelect.style.color = "white";
          fontSizeSelect.style.border = "none";
          fontSizeSelect.style.borderRadius = "4px";

          [
            2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 40, 44, 48, 52,
            56, 60, 64, 72, 80,
          ].forEach((size) => {
            const option = document.createElement("option");
            option.value = size;
            option.textContent = size + "px";
            if (size === simpleElement.textSize) {
              option.selected = true;
            }
            fontSizeSelect.appendChild(option);
          });
          popup.appendChild(fontSizeSelect);

          // Font Family
          const fontFamilyLabel = document.createElement("div");
          fontFamilyLabel.style.color = "white";
          fontFamilyLabel.style.marginBottom = "10px";
          fontFamilyLabel.textContent = "Font Family";
          popup.appendChild(fontFamilyLabel);

          const fontFamilySelect = document.createElement("select");
          fontFamilySelect.style.width = "100%";
          fontFamilySelect.style.marginBottom = "15px";
          fontFamilySelect.style.padding = "8px";
          fontFamilySelect.style.backgroundColor = "#333740";
          fontFamilySelect.style.color = "white";
          fontFamilySelect.style.border = "none";
          fontFamilySelect.style.borderRadius = "4px";

          const fontFamilies = [
            { name: "Arial", value: "Arial, sans-serif" },
            { name: "Courier New", value: '"Courier New", Courier, monospace' },
            {
              name: "Times New Roman",
              value: '"Times New Roman", Times, serif',
            },
            { name: "Georgia", value: "Georgia, serif" },
            { name: "Verdana", value: "Verdana, sans-serif" },
          ];

          fontFamilies.forEach((font) => {
            const option = document.createElement("option");
            option.value = font.name;
            option.textContent = font.name;
            if (font.name === simpleElement.textFont) {
              option.selected = true;
            }
            fontFamilySelect.appendChild(option);
          });
          popup.appendChild(fontFamilySelect);

          // Font Weight
          const fontWeightLabel = document.createElement("div");
          fontWeightLabel.style.color = "white";
          fontWeightLabel.style.marginBottom = "10px";
          fontWeightLabel.textContent = "Font Weight";
          popup.appendChild(fontWeightLabel);

          const fontWeightSelect = document.createElement("select");
          fontWeightSelect.style.width = "100%";
          fontWeightSelect.style.marginBottom = "15px";
          fontWeightSelect.style.padding = "8px";
          fontWeightSelect.style.backgroundColor = "#333740";
          fontWeightSelect.style.color = "white";
          fontWeightSelect.style.border = "none";
          fontWeightSelect.style.borderRadius = "4px";

          ["normal", "bold", "lighter", "bolder"].forEach((weight) => {
            const option = document.createElement("option");
            option.value = weight;
            option.textContent = weight;
            if (weight === simpleElement.textWeight) {
              option.selected = true;
            }
            fontWeightSelect.appendChild(option);
          });
          popup.appendChild(fontWeightSelect);

          // Text Align
          const textAlignLabel = document.createElement("div");
          textAlignLabel.style.color = "white";
          textAlignLabel.style.marginBottom = "10px";
          textAlignLabel.textContent = "Text Align";
          popup.appendChild(textAlignLabel);

          const textAlignSelect = document.createElement("select");
          textAlignSelect.style.width = "100%";
          textAlignSelect.style.marginBottom = "15px";
          textAlignSelect.style.padding = "8px";
          textAlignSelect.style.backgroundColor = "#333740";
          textAlignSelect.style.color = "white";
          textAlignSelect.style.border = "none";
          textAlignSelect.style.borderRadius = "4px";

          ["left", "center", "right"].forEach((align) => {
            const option = document.createElement("option");
            option.value = align;
            option.textContent = align;
            if (align === simpleElement.textAlign) {
              option.selected = true;
            }
            textAlignSelect.appendChild(option);
          });
          popup.appendChild(textAlignSelect);

          // Text Color
          const textColorLabel = document.createElement("div");
          textColorLabel.style.color = "white";
          textColorLabel.style.marginBottom = "10px";
          textColorLabel.textContent = "Text Color";
          popup.appendChild(textColorLabel);

          const textColorPicker = document.createElement("input");
          textColorPicker.type = "color";
          textColorPicker.value = simpleElement.textColor || "#000000";
          textColorPicker.style.width = "100%";
          textColorPicker.style.marginBottom = "15px";
          textColorPicker.style.height = "40px";
          textColorPicker.style.backgroundColor = "#333740";
          textColorPicker.style.border = "none";
          textColorPicker.style.borderRadius = "4px";
          popup.appendChild(textColorPicker);

          // Close button
          const closeButton = document.createElement("button");
          closeButton.textContent = "Close";
          closeButton.style.width = "100%";
          closeButton.style.padding = "8px";
          closeButton.style.backgroundColor = "#1976d2";
          closeButton.style.color = "white";
          closeButton.style.border = "none";
          closeButton.style.borderRadius = "4px";
          closeButton.style.cursor = "pointer";
          popup.appendChild(closeButton);

          // Add event listeners
          fontSizeSelect.addEventListener("change", async () => {
            const newSize = parseInt(fontSizeSelect.value);
            const updatedElement = deepClone(simpleElement);
            updatedElement.textSize = newSize;
            await store.dispatch("updateSimpleElement", {
              simpleElement: updatedElement,
              undoable: true,
            });
            updateGraphicElementFromState(element.id);

            // Update element name style
            if (element.graphicElement.elementName) {
              element.graphicElement.elementName.attr(
                "style",
                element.graphicElement.elementName
                  .attr("style")
                  .replace(/font-size: \d+px/, `font-size: ${newSize}px`),
              );
            }
          });

          fontFamilySelect.addEventListener("change", async () => {
            const newFont = fontFamilySelect.value;
            const updatedElement = deepClone(simpleElement);
            updatedElement.textFont = newFont;
            await store.dispatch("updateSimpleElement", {
              simpleElement: updatedElement,
              undoable: true,
            });
            updateGraphicElementFromState(element.id);

            // Update element name style
            if (element.graphicElement.elementName) {
              element.graphicElement.elementName.attr(
                "style",
                element.graphicElement.elementName
                  .attr("style")
                  .replace(
                    /font-family: [^;]+;/,
                    `font-family: ${newFont}, Helvetica, sans-serif;`,
                  ),
              );
            }
          });

          fontWeightSelect.addEventListener("change", async () => {
            const newWeight = fontWeightSelect.value;
            const updatedElement = deepClone(simpleElement);
            updatedElement.textWeight = newWeight;
            await store.dispatch("updateSimpleElement", {
              simpleElement: updatedElement,
              undoable: true,
            });
            updateGraphicElementFromState(element.id);

            // Update element name style
            if (element.graphicElement.elementName) {
              element.graphicElement.elementName.attr(
                "style",
                element.graphicElement.elementName
                  .attr("style")
                  .replace(/font-weight: [^;]+;/, `font-weight: ${newWeight};`),
              );
            }
          });

          textAlignSelect.addEventListener("change", async () => {
            const newAlign = textAlignSelect.value;
            const updatedElement = deepClone(simpleElement);
            updatedElement.textAlign = newAlign;
            await store.dispatch("updateSimpleElement", {
              simpleElement: updatedElement,
              undoable: true,
            });
            updateGraphicElementFromState(element.id);

            // Update element name style
            if (element.graphicElement.elementName) {
              const currentStyle =
                element.graphicElement.elementName.attr("style") || "";
              if (currentStyle.includes("text-align")) {
                element.graphicElement.elementName.attr(
                  "style",
                  currentStyle.replace(
                    /text-align: [^;]+;/,
                    `text-align: ${newAlign};`,
                  ),
                );
              } else {
                element.graphicElement.elementName.attr(
                  "style",
                  currentStyle + ` text-align: ${newAlign};`,
                );
              }
            }
          });

          textColorPicker.addEventListener("input", async () => {
            const newColor = textColorPicker.value;
            const updatedElement = deepClone(simpleElement);
            updatedElement.textColor = newColor;
            await store.dispatch("updateSimpleElement", {
              simpleElement: updatedElement,
              undoable: true,
            });
            updateGraphicElementFromState(element.id);

            // Update element name color
            if (element.graphicElement.elementName) {
              element.graphicElement.elementName.fill(newColor);
            }
          });

          closeButton.addEventListener("click", () => {
            document.body.removeChild(popup);
            hideContextMenu();
          });

          document.body.appendChild(popup);
          hideContextMenu();
        }
      };

      // Add click handlers to background, title and icon
      sectionBg.on("click", handleActionClick);
      sectionTitle.on("click", handleActionClick);
      sectionIcon.on("click", handleActionClick);
    }

    // Increment currentY for the next section
    currentY += itemHeight + 10;
  });

  // Function to create submenu
  function createSubmenu(
    items,
    submenuX,
    submenuY,
    width,
    originalEvent,
    element,
  ) {
    const submenu = draw.group().attr("id", "context-submenu");

    // Create submenu background
    submenu
      .rect(width, items.length * itemHeight + 10)
      .radius(8)
      .fill("#ffffff")
      .opacity(0.95)
      .stroke({ color: "#3c4365", width: 1 })
      .move(submenuX, submenuY);

    // Create submenu items
    items.forEach((item, index) => {
      const itemY = submenuY + index * itemHeight + 5;

      // Item background first (so it's behind other elements) - make it fit within submenu borders
      const itemBg = submenu
        .rect(width - 2, itemHeight - 5)
        .radius(4)
        .fill("transparent")
        .move(submenuX + 1, itemY);

      // Item icon
      const itemIcon = submenu
        .path(item.icon)
        .fill("#555555")
        .size(16, 16)
        .move(submenuX + 15, itemY + 5);

      // Item text
      const itemText = submenu
        .text(item.label)
        .font({ family: "Arial", size: 12 })
        .fill("#000000")
        .move(submenuX + 40, itemY + 5);

      // Function to handle hover effect
      const handleMouseOver = () => {
        itemBg.fill("#f0f0f0");
      };

      const handleMouseLeave = () => {
        itemBg.fill("transparent");
      };

      // Function to handle click
      const handleClick = async () => {
        const isMultipleSelection =
          store.getters.currentWorkspaceSelectedElementsIds.length > 1;

        if (item.action === "selectAll") {
          await selectAllElements();
        } else if (item.type) {
          // Update element shape
          const simpleElement = deepClone(
            store.state.allSimpleElements.get(element.id),
          );
          simpleElement.type = item.type;
          await store.dispatch("updateSimpleElement", {
            simpleElement,
            undoable: true,
          });
          updateGraphicElementFromState(element.id);
        } else if (
          item.action === "moveToNewWorkspace" ||
          item.action === "moveMultipleToNewWorkspace"
        ) {
          // Calculate the position for the new workspace element
          const posX =
            originalEvent.offsetX * (draw.viewbox().width / 10000) +
            draw.viewbox().x;
          const posY =
            originalEvent.offsetY * (draw.viewbox().width / 10000) +
            draw.viewbox().y;

          if (isMultipleSelection) {
            // Move multiple elements to a new workspace
            await moveSelectedElementsToWorkspace(null, async () => {
              // Create a new element first
              const newWorskspaceElement = await createNewElementAtPosition(
                posX,
                posY,
                "SQUARE",
                "New\nWorkspace",
              );
              // Create a new workspace and return its ID
              return await createWorkspace(newWorskspaceElement.id);
            });
          } else {
            // Move a single element to a new workspace
            await moveElementToWorkspace(element.id, null, null, async () => {
              // This callback will only be executed if the user confirms the move
              // Create a new element first
              const newWorskspaceElement = await createNewElementAtPosition(
                posX,
                posY,
                "SQUARE",
                "New\nWorkspace",
              );
              // Create a new workspace and return its ID
              return await createWorkspace(newWorskspaceElement.id);
            });
          }
        } else if (item.workspaceId) {
          if (isMultipleSelection) {
            // Move all selected elements to the selected workspace
            await moveSelectedElementsToWorkspace(item.workspaceId);
          } else {
            // Move the element to the selected workspace
            await moveElementToWorkspace(element.id, item.workspaceId);
          }
        }
        hideContextMenu();
      };

      // Add event listeners to all elements
      itemBg.on("mouseover", handleMouseOver);
      itemBg.on("mouseleave", handleMouseLeave);
      itemBg.on("click", handleClick);

      // Add same event listeners to icon and text
      itemIcon.on("mouseover", handleMouseOver);
      itemIcon.on("mouseleave", handleMouseLeave);
      itemIcon.on("click", handleClick);

      itemText.on("mouseover", handleMouseOver);
      itemText.on("mouseleave", handleMouseLeave);
      itemText.on("click", handleClick);
    });

    return submenu;
  }

  // Add click handler to document to close menu when clicking outside
  documentClickHandler = (evt) => {
    // Check if the click is on the context menu or submenu
    const contextMenuElement = document.getElementById("context-menu");
    const submenuElement = document.getElementById("context-submenu");

    // Don't close if clicking on the menu or submenu
    if (
      (contextMenuElement && contextMenuElement.contains(evt.target)) ||
      (submenuElement && submenuElement.contains(evt.target))
    ) {
      return;
    }

    // Otherwise, hide the menu
    hideContextMenu();
  };

  document.addEventListener("click", documentClickHandler);

  // Prevent default context menu
  e.preventDefault();
};

export const setupElementEntityListeners = (elementEntity, element) => {
  elementEntity
    .on("click", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      // Don't allow clicking on elements from imported sequences
      if (isElementFromImportedSequence(element.id)) {
        return;
      }
      if (e.ctrlKey || e.metaKey) {
        if (
          store.getters.currentWorkspaceSelectedElementsIds.includes(element.id)
        ) {
          removeElementFromSelection(element.id);
        } else {
          await addElementToSelection({
            elementId: element.id,
            undoable: false,
          });
        }
      }
      drawAdapter.parameters.multiObjectsMoveInitialPosition = {};
      drawAdapter.parameters.multiObjectsMoveCurrentPosition = {};
      drawAdapter.parameters.draggingStarted = false;
    })
    .on("contextmenu", (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      // Don't allow context menu on elements from imported sequences
      if (isElementFromImportedSequence(element.id)) {
        return;
      }

      // If the element is not in the current selection, select it first
      if (
        !store.getters.currentWorkspaceSelectedElementsIds.includes(element.id)
      ) {
        addElementToSelection({
          elementId: element.id,
          deselectOthers: true,
          undoable: false,
        });
      }

      // Show context menu at mouse position
      showElementContextMenu(e, element);
    })
    .on("mousedown", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      // Don't allow mousedown on elements from imported sequences
      if (isElementFromImportedSequence(element.id)) {
        return;
      }
      document.activeElement.blur();
      if (
        !store.getters.currentWorkspaceSelectedElementsIds.includes(
          element.id,
        ) &&
        !e.ctrlKey &&
        !e.metaKey
      ) {
        await addElementToSelection({
          elementId: element.id,
          deselectOthers: true,
          undoable: false,
        });
        unselectAllSegments();
      }
    });
};

export const setupExpandIconListeners = (element) => {
  // Add expand icon and action menu listeners
  const expandIcon = element.graphicElement.expandIcon;

  expandIcon.on("mouseover", async () => {
    if (
      !element.graphicElement.actionMenuBackground &&
      !element.graphicElement.deleteBtn &&
      !element.graphicElement.workspaceBtn
    ) {
      // Hide the workspace expand button when showing the menu
      if (element.graphicElement.workspaceExpandBtnGroup) {
        element.graphicElement.workspaceExpandBtnGroup.hide();
      }
      // Create white background
      element.graphicElement.actionMenuBackground = element.graphicElement
        .rect(100, 20)
        .move(
          element.graphicElement.entity.x(),
          element.graphicElement.entity.y() - 25,
        )
        .fill("white")
        .stroke({ color: "#3c4365", width: 1 })
        .radius(6)
        .attr({ cursor: "default" });

      // Create invisible square backgrounds for easier clicking (18x18 px each)
      element.graphicElement.workspaceBtnClickArea = element.graphicElement
        .rect(18, 18)
        .move(
          element.graphicElement.entity.x() + 5 - 2,
          element.graphicElement.entity.y() - 23 - 2,
        )
        .fill("transparent")
        .opacity(0)
        .attr({ cursor: "pointer" });

      element.graphicElement.renameBtnClickArea = element.graphicElement
        .rect(18, 18)
        .move(
          element.graphicElement.entity.x() + 24 - 2,
          element.graphicElement.entity.y() - 23 - 2,
        )
        .fill("transparent")
        .opacity(0)
        .attr({ cursor: "pointer" });

      element.graphicElement.colorBtnClickArea = element.graphicElement
        .rect(18, 18)
        .move(
          element.graphicElement.entity.x() + 43 - 2,
          element.graphicElement.entity.y() - 23 - 2,
        )
        .fill("transparent")
        .opacity(0)
        .attr({ cursor: "pointer" });

      element.graphicElement.textBtnClickArea = element.graphicElement
        .rect(18, 18)
        .move(
          element.graphicElement.entity.x() + 62 - 2,
          element.graphicElement.entity.y() - 23 - 2,
        )
        .fill("transparent")
        .opacity(0)
        .attr({ cursor: "pointer" });

      element.graphicElement.deleteBtnClickArea = element.graphicElement
        .rect(18, 18)
        .move(
          element.graphicElement.entity.x() + 81 - 2,
          element.graphicElement.entity.y() - 23 - 2,
        )
        .fill("transparent")
        .opacity(0)
        .attr({ cursor: "pointer" });

      // Create workspace button (git-style fork icon)
      element.graphicElement.workspaceBtn = element.graphicElement
        .path(
          "M12 21a1.75 1.75 0 1 1 0-3.5 1.75 1.75 0 0 1 0 3.5zm-3.25-1.75a3.25 3.25 0 1 0 6.5 0 3.25 3.25 0 0 0-6.5 0zm-3-12.75a1.75 1.75 0 1 1 0-3.5 1.75 1.75 0 0 1 0 3.5zM2.5 4.5a3.25 3.25 0 1 0 6.5 0 3.25 3.25 0 0 0-6.5 0zm18.5 6.75a1.75 1.75 0 1 1 0-3.5 1.75 1.75 0 0 1 0 3.5zM15.5 7.75a3.25 3.25 0 1 0 6.5 0 3.25 3.25 0 0 0-6.5 0z",
        )
        .move(
          element.graphicElement.entity.x() + 5,
          element.graphicElement.entity.y() - 23,
        )
        .fill("#3c4365")
        .attr({ cursor: "pointer" })
        .size(14, 14);

      // Create rename button (pencil icon)
      element.graphicElement.renameBtn = element.graphicElement
        .path(
          "M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z",
        )
        .move(
          element.graphicElement.entity.x() + 24,
          element.graphicElement.entity.y() - 23,
        )
        .fill("#3c4365")
        .attr({ cursor: "pointer" })
        .size(14, 14);

      // Create color button (color palette icon)
      element.graphicElement.colorBtn = element.graphicElement
        .path(
          "M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z",
        )
        .move(
          element.graphicElement.entity.x() + 43,
          element.graphicElement.entity.y() - 23,
        )
        .fill("#3c4365")
        .attr({ cursor: "pointer" })
        .size(14, 14);

      // Create text properties button (single capital T icon)
      element.graphicElement.textBtn = element.graphicElement
        .path("M5 4v3h5.5v12h3V7H19V4z")
        .move(
          element.graphicElement.entity.x() + 62,
          element.graphicElement.entity.y() - 23,
        )
        .fill("#3c4365")
        .attr({ cursor: "pointer" })
        .size(14, 14);

      // Create delete button
      element.graphicElement.deleteBtn = element.graphicElement
        .path(
          "M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z",
        )
        .move(
          element.graphicElement.entity.x() + 81,
          element.graphicElement.entity.y() - 23,
        )
        .fill("#3c4365")
        .attr({ cursor: "pointer" })
        .size(14, 14);

      element.graphicElement.front();

      setupElementIconsListeners(
        element,
        element.graphicElement.deleteBtn,
        element.graphicElement.renameBtn,
        element.graphicElement.colorBtn,
        element.graphicElement.textBtn,
        element.graphicElement.workspaceBtn,
      );
    }
  });

  expandIcon.on("mouseleave", () => {
    // Only hide if mouse is not over any of the menu items
    setTimeout(() => {
      if (
        !element.graphicElement.actionMenuBackground?.node.matches(":hover") &&
        !element.graphicElement.deleteBtn?.node.matches(":hover") &&
        !element.graphicElement.renameBtn?.node.matches(":hover") &&
        !element.graphicElement.colorBtn?.node.matches(":hover") &&
        !element.graphicElement.textBtn?.node.matches(":hover") &&
        !element.graphicElement.workspaceBtn?.node.matches(":hover")
      ) {
        element.graphicElement.actionMenuBackground?.remove();
        element.graphicElement.deleteBtn?.remove();
        element.graphicElement.renameBtn?.remove();
        element.graphicElement.colorBtn?.remove();
        element.graphicElement.textBtn?.remove();
        element.graphicElement.workspaceBtn?.remove();
        element.graphicElement.workspaceBtnClickArea?.remove();
        element.graphicElement.renameBtnClickArea?.remove();
        element.graphicElement.colorBtnClickArea?.remove();
        element.graphicElement.textBtnClickArea?.remove();
        element.graphicElement.deleteBtnClickArea?.remove();
        element.graphicElement.actionMenuBackground = null;
        element.graphicElement.deleteBtn = null;
        element.graphicElement.renameBtn = null;
        element.graphicElement.colorBtn = null;
        element.graphicElement.textBtn = null;
        element.graphicElement.workspaceBtn = null;
        element.graphicElement.workspaceBtnClickArea = null;
        element.graphicElement.renameBtnClickArea = null;
        element.graphicElement.colorBtnClickArea = null;
        element.graphicElement.textBtnClickArea = null;
        element.graphicElement.deleteBtnClickArea = null;

        // Show the workspace expand button again when the menu is hidden
        if (element.graphicElement.workspaceExpandBtnGroup) {
          element.graphicElement.workspaceExpandBtnGroup.show();
        }
      }
    }, 100);
  });
};

const setupElementIconsListeners = (
  element,
  deleteBtn,
  renameBtn,
  colorBtn,
  textBtn,
  workspaceBtn,
) => {
  // Delete button and its click area
  const deleteAction = async () => {
    await deleteElement({
      elementId: element.id,
      force: false,
      undoable: true,
    });
  };

  deleteBtn
    .on("click", deleteAction)
    .on("mouseenter", () => {
      deleteBtn.animate(200).fill("#d32f2f");
    })
    .on("mouseleave", () => {
      deleteBtn.animate(200).fill("#3c4365");
    });

  // Add click listener to invisible click area
  element.graphicElement.deleteBtnClickArea?.on("click", deleteAction);

  // Rename button and its click area
  const renameAction = () => {
    element.graphicElement.elementName.dispatchEvent(new Event("dblclick"));
  };

  renameBtn
    .on("click", renameAction)
    .on("mouseenter", () => {
      renameBtn.animate(200).fill("#1976d2");
    })
    .on("mouseleave", () => {
      renameBtn.animate(200).fill("#3c4365");
    });

  // Add click listener to invisible click area
  element.graphicElement.renameBtnClickArea?.on("click", renameAction);

  // Color button and its click area
  const colorAction = () => {
    // Trigger color picker
    const colorPicker = document.createElement("input");
    colorPicker.type = "color";
    colorPicker.value = element.graphicElement.entity.attr("fill");
    colorPicker.addEventListener("input", async (e) => {
      const newColor = e.target.value;
      element.graphicElement.entity.fill(newColor);
      await store.dispatch("updateElement", { element, undoable: true });
    });
    colorPicker.click();
  };

  colorBtn
    .on("click", colorAction)
    .on("mouseenter", () => {
      colorBtn.animate(200).fill("#1976d2");
    })
    .on("mouseleave", () => {
      colorBtn.animate(200).fill("#3c4365");
    });

  // Add click listener to invisible click area
  element.graphicElement.colorBtnClickArea?.on("click", colorAction);

  // Text properties button and its click area
  const textAction = () => {
    // Create text properties popup
    const popup = document.createElement("div");
    popup.style.position = "fixed";
    popup.style.top = "50%";
    popup.style.left = "50%";
    popup.style.transform = "translate(-50%, -50%)";
    popup.style.backgroundColor = "#2a2d32ff";
    popup.style.padding = "20px";
    popup.style.borderRadius = "8px";
    popup.style.boxShadow = "0 0 10px rgba(0,0,0,0.5)";
    popup.style.zIndex = "1000";

    // Font Size
    const fontSizeLabel = document.createElement("div");
    fontSizeLabel.style.color = "white";
    fontSizeLabel.style.marginBottom = "10px";
    fontSizeLabel.textContent = "Font Size";
    popup.appendChild(fontSizeLabel);

    const fontSizeSelect = document.createElement("select");
    fontSizeSelect.style.width = "100%";
    fontSizeSelect.style.marginBottom = "15px";
    fontSizeSelect.style.backgroundColor = "#333740";
    fontSizeSelect.style.color = "white";
    fontSizeSelect.style.padding = "5px";
    fontSizeSelect.style.border = "none";
    fontSizeSelect.style.borderRadius = "4px";
    [
      2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56,
      60, 64, 72, 80,
    ].forEach((size) => {
      const option = document.createElement("option");
      option.value = size;
      option.textContent = size;
      if (
        size ===
        element.graphicElement.elementName
          .attr("style")
          .match(/font-size: (\d+)px/)[1]
      ) {
        option.selected = true;
      }
      fontSizeSelect.appendChild(option);
    });
    popup.appendChild(fontSizeSelect);

    // Font Family
    const fontFamilyLabel = document.createElement("div");
    fontFamilyLabel.style.color = "white";
    fontFamilyLabel.style.marginBottom = "10px";
    fontFamilyLabel.textContent = "Font Family";
    popup.appendChild(fontFamilyLabel);

    const fontFamilySelect = document.createElement("select");
    fontFamilySelect.style.width = "100%";
    fontFamilySelect.style.marginBottom = "15px";
    fontFamilySelect.style.backgroundColor = "#333740";
    fontFamilySelect.style.color = "white";
    fontFamilySelect.style.padding = "5px";
    fontFamilySelect.style.border = "none";
    fontFamilySelect.style.borderRadius = "4px";
    const fontFamilies = [
      { name: "Arial", value: "Arial, sans-serif" },
      { name: "Courier New", value: '"Courier New", Courier, monospace' },
      { name: "Times New Roman", value: '"Times New Roman", Times, serif' },
      { name: "Georgia", value: "Georgia, serif" },
      { name: "Verdana", value: "Verdana, sans-serif" },
    ];
    fontFamilies.forEach((font) => {
      const option = document.createElement("option");
      option.value = font.value;
      option.textContent = font.name;
      if (
        font.value ===
        element.graphicElement.elementName
          .attr("style")
          .match(/font-family: ([^,]+)/)[1]
      ) {
        option.selected = true;
      }
      fontFamilySelect.appendChild(option);
    });
    popup.appendChild(fontFamilySelect);

    // Font Weight
    const fontWeightLabel = document.createElement("div");
    fontWeightLabel.style.color = "white";
    fontWeightLabel.style.marginBottom = "10px";
    fontWeightLabel.textContent = "Font Weight";
    popup.appendChild(fontWeightLabel);

    const fontWeightSelect = document.createElement("select");
    fontWeightSelect.style.width = "100%";
    fontWeightSelect.style.marginBottom = "15px";
    fontWeightSelect.style.backgroundColor = "#333740";
    fontWeightSelect.style.color = "white";
    fontWeightSelect.style.padding = "5px";
    fontWeightSelect.style.border = "none";
    fontWeightSelect.style.borderRadius = "4px";
    ["normal", "bold", "lighter"].forEach((weight) => {
      const option = document.createElement("option");
      option.value = weight;
      option.textContent = weight;
      if (
        weight ===
        element.graphicElement.elementName
          .attr("style")
          .match(/font-weight: ([^;]+)/)[1]
      ) {
        option.selected = true;
      }
      fontWeightSelect.appendChild(option);
    });
    popup.appendChild(fontWeightSelect);

    // Text Color
    const textColorLabel = document.createElement("div");
    textColorLabel.style.color = "white";
    textColorLabel.style.marginBottom = "10px";
    textColorLabel.textContent = "Text Color";
    popup.appendChild(textColorLabel);

    const textColorPicker = document.createElement("input");
    textColorPicker.type = "color";
    textColorPicker.value = element.graphicElement.elementName.attr("fill");
    textColorPicker.style.width = "100%";
    textColorPicker.style.marginBottom = "15px";
    textColorPicker.style.height = "40px";
    textColorPicker.style.backgroundColor = "#333740";
    textColorPicker.style.border = "none";
    textColorPicker.style.borderRadius = "4px";
    popup.appendChild(textColorPicker);

    // Close button
    const closeButton = document.createElement("button");
    closeButton.textContent = "Close";
    closeButton.style.width = "100%";
    closeButton.style.padding = "8px";
    closeButton.style.backgroundColor = "#1976d2";
    closeButton.style.color = "white";
    closeButton.style.border = "none";
    closeButton.style.borderRadius = "4px";
    closeButton.style.cursor = "pointer";
    popup.appendChild(closeButton);

    // Add event listeners
    fontSizeSelect.addEventListener("change", async () => {
      const newSize = fontSizeSelect.value;
      element.graphicElement.elementName.attr(
        "style",
        element.graphicElement.elementName
          .attr("style")
          .replace(/font-size: \d+px/, `font-size: ${newSize}px`),
      );
      await store.dispatch("updateElement", { element, undoable: true });
    });

    fontFamilySelect.addEventListener("change", async () => {
      const newFont = fontFamilySelect.value;
      element.graphicElement.elementName.attr(
        "style",
        element.graphicElement.elementName
          .attr("style")
          .replace(/font-family: [^,]+/, `font-family: ${newFont}`),
      );
      await store.dispatch("updateElement", { element, undoable: true });
    });

    fontWeightSelect.addEventListener("change", async () => {
      const newWeight = fontWeightSelect.value;
      element.graphicElement.elementName.attr(
        "style",
        element.graphicElement.elementName
          .attr("style")
          .replace(/font-weight: [^;]+/, `font-weight: ${newWeight}`),
      );
      await store.dispatch("updateElement", { element, undoable: true });
    });

    textColorPicker.addEventListener("input", async () => {
      const newColor = textColorPicker.value;
      element.graphicElement.elementName.fill(newColor);
      await store.dispatch("updateElement", { element, undoable: true });
    });

    closeButton.addEventListener("click", () => {
      document.body.removeChild(popup);
    });

    document.body.appendChild(popup);
  };

  textBtn
    .on("click", textAction)
    .on("mouseenter", () => {
      textBtn.animate(200).fill("#1976d2");
    })
    .on("mouseleave", () => {
      textBtn.animate(200).fill("#3c4365");
    });

  // Add click listener to invisible click area
  element.graphicElement.textBtnClickArea?.on("click", textAction);

  // Only show workspace button in creation mode
  if (store.state.diagramMode === "CREATION_MODE") {
    const simpleElement = store.state.allSimpleElements.get(element.id);
    if (simpleElement && simpleElement.childWorkspaceId) {
      // If element has a workspace, show enter workspace icon (fork with down arrow)
      const enterWorkspaceAction = async () => {
        await enterWorkspaceWithZoom(
          simpleElement.childWorkspaceId,
          element.id,
        );
      };

      workspaceBtn
        .plot(
          "M -4 -5 L -9 -5 L -9 -10 L -7 -10 L -12 -16 L -17 -10 L -15 -10 L -15 -5 L -20 -5 L -20 -10 L -18 -10 L -13 -16 C -14.19 -16.62 -15 -17.72 -15 -19 A 3 3 180 0 1 -12 -22 A 3 3 180 0 1 -9 -19 C -9 -17.72 -9.81 -16.62 -11 -16 L -6 -10 L -4 -10 L -4 -5 Z M -5 -12 L -3 -15 L -4 -15 L -4 -18 L -6 -18 L -6 -15 L -7 -15 Z",
        )
        .move(
          element.graphicElement.entity.x() + 5,
          element.graphicElement.entity.y() - 23,
        )
        .size(14, 14)
        .on("click", enterWorkspaceAction)
        .on("mouseenter", () => {
          workspaceBtn.animate(200).fill("#1976d2");
        })
        .on("mouseleave", () => {
          workspaceBtn.animate(200).fill("#3c4365");
        });

      // Add click listener to invisible click area
      element.graphicElement.workspaceBtnClickArea?.on(
        "click",
        enterWorkspaceAction,
      );
    } else {
      // If element doesn't have a workspace, show create workspace icon (fork with plus)
      const createWorkspaceAction = async () => {
        const elementId = element.id;
        const workspaceId = await createWorkspace(elementId);
        // Update the button to show enter workspace icon
        workspaceBtn
          .plot(
            "M -4 -5 L -9 -5 L -9 -10 L -7 -10 L -12 -16 L -17 -10 L -15 -10 L -15 -5 L -20 -5 L -20 -10 L -18 -10 L -13 -16 C -14.19 -16.62 -15 -17.72 -15 -19 A 3 3 180 0 1 -12 -22 A 3 3 180 0 1 -9 -19 C -9 -17.72 -9.81 -16.62 -11 -16 L -6 -10 L -4 -10 L -4 -5 Z M -5 -12 L -3 -15 L -4 -15 L -4 -18 L -6 -18 L -6 -15 L -7 -15 Z",
          )
          .move(
            element.graphicElement.entity.x() + 5,
            element.graphicElement.entity.y() - 23,
          )
          .size(14, 14)
          .off("click")
          .on("click", async () => {
            await enterWorkspaceWithZoom(workspaceId, element.id);
          });
        await enterWorkspaceWithZoom(workspaceId, element.id);
      };

      workspaceBtn
        .plot(
          "M -4 -5 L -9 -5 L -9 -10 L -7 -10 L -12 -16 L -17 -10 L -15 -10 L -15 -5 L -20 -5 L -20 -10 L -18 -10 L -13 -16 C -14.19 -16.62 -15 -17.72 -15 -19 A 3 3 180 0 1 -12 -22 A 3 3 180 0 1 -9 -19 C -9 -17.72 -9.81 -16.62 -11 -16 L -6 -10 L -4 -10 L -4 -5 Z M -5 -12 L -5 -14 L -3 -14 L -3 -15 L -5 -15 L -5 -17 L -6 -17 L -6 -15 L -8 -15 L -8 -14 L -6 -14 L -6 -12 Z",
        )
        .move(
          element.graphicElement.entity.x() + 5,
          element.graphicElement.entity.y() - 23,
        )
        .size(14, 14)
        .on("click", createWorkspaceAction)
        .on("mouseenter", () => {
          workspaceBtn.animate(200).fill("#1976d2");
        })
        .on("mouseleave", () => {
          workspaceBtn.animate(200).fill("#3c4365");
        });

      // Add click listener to invisible click area
      element.graphicElement.workspaceBtnClickArea?.on(
        "click",
        createWorkspaceAction,
      );
    }
  } else {
    // Hide workspace button in non-creation modes
    workspaceBtn.hide();
  }

  // We no longer need to setup the expand/collapse workspace button here
  // It's now handled directly in the workspace expand button on the top right of the element
};

async function selectObjectsWithRectangularSelection(
  simpleElement,
  initialX,
  initialY,
  selectFunction,
  unselectFunction,
) {
  // selection between entity bounds
  let rectangleSelectionWidth =
    drawAdapter.parameters.rectangleSelection.entity.width();
  let rectangleSelectionHeight =
    drawAdapter.parameters.rectangleSelection.entity.height();
  let simpleObjectCx =
    simpleElement.workspaceParameters[store.state.currentWorkspaceId].x;
  let simpleObjectCy =
    simpleElement.workspaceParameters[store.state.currentWorkspaceId].y;
  if (
    Math.min(initialX, drawAdapter.parameters.rectangleSelection.entity.x()) <=
      simpleObjectCx &&
    simpleObjectCx <=
      Math.min(initialX, drawAdapter.parameters.rectangleSelection.entity.x()) +
        rectangleSelectionWidth &&
    Math.min(initialY, drawAdapter.parameters.rectangleSelection.entity.y()) <=
      simpleObjectCy &&
    simpleObjectCy <=
      Math.min(initialY, drawAdapter.parameters.rectangleSelection.entity.y()) +
        rectangleSelectionHeight
  ) {
    await selectFunction({
      elementId: simpleElement.id,
      deselectOthers: false,
    });
  } else {
    unselectFunction(simpleElement.id);
  }
}

async function drawRectangleSelection(e) {
  let initialX = drawAdapter.parameters.rectangleSelection.initialX;
  if (
    initialX >
    e.offsetX * (draw.viewbox().width / 10000) + draw.viewbox().x
  ) {
    drawAdapter.parameters.rectangleSelection.entity.x(
      e.offsetX * (draw.viewbox().width / 10000) + draw.viewbox().x,
    );
  }
  let initialY = drawAdapter.parameters.rectangleSelection.initialY;
  if (
    initialY >
    e.offsetY * (draw.viewbox().width / 10000) + draw.viewbox().y
  ) {
    drawAdapter.parameters.rectangleSelection.entity.y(
      e.offsetY * (draw.viewbox().width / 10000) + draw.viewbox().y,
    );
  }
  drawAdapter.parameters.rectangleSelection.entity
    .width(
      Math.abs(
        initialX -
          (e.offsetX * (draw.viewbox().width / 10000) + draw.viewbox().x),
      ),
    )
    .height(
      Math.abs(
        initialY -
          (e.offsetY * (draw.viewbox().width / 10000) + draw.viewbox().y),
      ),
    );
  for (const simpleElementId of store.getters.currentWorkspaceElements) {
    const simpleElement = store.state.allSimpleElements.get(simpleElementId);
    await selectObjectsWithRectangularSelection(
      simpleElement,
      initialX,
      initialY,
      addElementToSelection,
      unselectElement,
    );
  }
}
export const setupEditableElementNameListeners = (elementName, element) => {
  elementName.on("dblclick", () => {
    const simpleElement = store.state.allSimpleElements.get(element.id);
    if (
      store.state.sequences.get(store.state.currentSequenceId) &&
      store.state.sequences.get(store.state.currentSequenceId).currentFrame %
        1 !==
        0
    ) {
      return;
    }
    if (element.graphicElement.elementNameDiv) {
      return; // we are already editing, no need to do anything now
    }
    if (element.type === "TEXT-CLASSIC") {
      element.graphicElement.elementName.editableDiv.dispatchEvent(
        new Event("dblclick"),
      );
    } else {
      const editableDiv = document.createElement("div");
      editableDiv.setAttribute("contentEditable", "true");
      editableDiv.setAttribute("id", `editableName-${element.id}`);
      editableDiv.setAttribute(
        "style",
        `max-width: ${elementName.width}px; display: inline-block; white-space: pre; font-weight: ${simpleElement.textWeight}; font-size: ${simpleElement.textSize}px; font-family: ${simpleElement.textFont}, Helvetica, sans-serif; color: ${elementName.attr("fill")}; user-select: none; text-align: ${simpleElement.textAlign};`,
      );
      editableDiv.style.userSelect = "all";
      editableDiv.innerHTML = element.name.replaceAll("\n", "<br/>");
      element.graphicElement.elementNameDiv =
        element.graphicElement.foreignObject(
          elementName.bbox().width + 5,
          Math.max(
            element.graphicElement.height() * 2,
            elementName.bbox().height,
          ),
        );
      element.graphicElement.elementNameDiv.add(editableDiv);
      element.graphicElement.elementNameDiv.x(
        element.graphicElement.elementName.ax(),
      );

      element.graphicElement.elementNameDiv.y(
        element.graphicElement.elementName.ay() - 20,
      );
      element.graphicElement.elementNameDiv.editableDiv = editableDiv;

      element.graphicElement.draggable(false);
      draw.panZoom({
        zoomMin: 0.2,
        zoomMax: 20,
        panning: false,
        zoomFactor: 0.2,
        oneFingerPan: true,
      });
      element.graphicElement.elementNameMover
        .move(
          element.graphicElement.elementName.ax(),
          element.graphicElement.elementName.ay() - 40,
        )
        .show();
      elementName.hide();
      editableDiv.focus();
      document.getSelection().selectAllChildren(editableDiv);
      setupEditableDivListeners(editableDiv, element);
    }
  });
  if (element.type !== "TEXT-CLASSIC") {
    element.graphicElement.elementNameMover
      .on("dragmove", () => {
        if (
          store.state.sequences.get(store.state.currentSequenceId) &&
          store.state.sequences.get(store.state.currentSequenceId)
            .currentFrame %
            1 !==
            0
        ) {
          return;
        }
        element.graphicElement.elementNameDiv?.editableDiv.blur();
        element.graphicElement.elementName.amove(
          element.graphicElement.elementNameMover.x(),
          element.graphicElement.elementNameMover.y() + 40,
        );
      })
      .on("dragend", () => {
        if (
          store.state.sequences.get(store.state.currentSequenceId) &&
          store.state.sequences.get(store.state.currentSequenceId)
            .currentFrame %
            1 !==
            0
        ) {
          return;
        }
        const storedElement = store.state.allSimpleElements.get(element.id);
        const workspaceParams =
          storedElement.workspaceParameters[store.state.currentWorkspaceId];
        // Convert absolute position to relative position
        workspaceParams.textX = elementName.ax() - workspaceParams.x;
        workspaceParams.textY = elementName.ay() - workspaceParams.y;
        store
          .dispatch("updateSimpleElement", {
            simpleElement: storedElement,
            undoable: true,
          })
          .then();
      })
      .on("mouseover", () => {
        if (
          store.state.sequences.get(store.state.currentSequenceId) &&
          store.state.sequences.get(store.state.currentSequenceId)
            .currentFrame %
            1 !==
            0
        ) {
          return;
        }
        element.graphicElement.elementNameMover
          .animate({ duration: 200, when: "now" })
          .fill("#62b646");
      })
      .on("mouseleave", () => {
        if (
          store.state.sequences.get(store.state.currentSequenceId) &&
          store.state.sequences.get(store.state.currentSequenceId)
            .currentFrame %
            1 !==
            0
        ) {
          return;
        }
        element.graphicElement.elementNameMover
          .animate({ duration: 200, when: "now" })
          .fill("#000000");
      });
  }
};

export const setupEditableDivListeners = (editableDiv, element) => {
  editableDiv.addEventListener("dblclick", () => {
    editableDiv.setAttribute("contentEditable", "true");
    editableDiv.focus();
    editableDiv.style.userSelect = "all";
    document.execCommand("selectAll", false, null);
    element.graphicElement.draggable(false);
    draw.panZoom({ panning: false });
    if (element.type !== "TEXT-CLASSIC") {
      element.graphicElement.elementNameMover
        .move(
          element.graphicElement.elementName.x(),
          element.graphicElement.elementName.y() - 40,
        )
        .show();
    }
  });
  editableDiv.addEventListener("keydown", async () => {
    if (element.type === "TEXT-CLASSIC") {
      element.graphicElement.elementName.height(editableDiv.offsetHeight);
      element.graphicElement.entity.height(
        element.graphicElement.elementName.height() + 5,
      );
      element.graphicElement.dummy.height(
        element.graphicElement.elementName.height() + 5,
      );
      element.graphicElement.dummyStroke.height(
        element.graphicElement.elementName.height() + 5,
      );
      element.graphicElement.resizer.move(
        element.graphicElement.entity.x() +
          element.graphicElement.entity.width() -
          15,
        element.graphicElement.entity.y() +
          element.graphicElement.entity.height() -
          15,
      );
      await store.dispatch("updateElement", { element, undoable: false });
      configureElementAnchors(element.id);
      retraceAllSegments(element.id);
      await store.dispatch("updateElement", { element, undoable: true }).then();
    } else {
      element.graphicElement.elementName.attr({
        height: editableDiv.offsetHeight,
      });
      element.graphicElement.elementNameDiv.attr({
        width: editableDiv.scrollWidth + 10,
      });
    }
  });
  editableDiv.addEventListener("blur", async () => {
    const storedElement = store.state.allSimpleElements.get(element.id);
    if (element.type === "TEXT-CLASSIC") {
      element.name = editableDiv.innerText;
      await store.dispatch("updateElement", { element, undoable: true }).then();
      editableDiv.setAttribute("contentEditable", "false");
      element.graphicElement.draggable();
      draw.panZoom({ panning: true });
      element.graphicElement.elementName.attr("pointer-events", "none");
      editableDiv.style.userSelect = "none";
      window.getSelection().removeAllRanges();
      element.graphicElement.elementNameMover?.hide();
    } else {
      element.name = editableDiv.innerHTML
        .replaceAll("<br>", "\n")
        .replaceAll("&nbsp;", " ");
      // This whole section below is because we can't have an empty element name as a svg text
      // otherwise moving the element gets craaaazy
      if (element.name === "") {
        element.graphicElement.elementName.remove();
      }
      await store.dispatch("updateElement", { element, undoable: true });
      editableDiv.remove();
      element.graphicElement.elementNameDiv.attr("transform", null);
      element.graphicElement.elementNameDiv.remove();
      element.graphicElement.elementNameDiv = null;
      element.graphicElement.elementName.text(element.name);
      element.graphicElement.elementName.show();
      element.graphicElement.draggable();
      setAppropriateSelectionMode();
      element.graphicElement.elementName.attr("cursor", "move");
      element.graphicElement.elementName.attr("pointer-events", "none");
      element.graphicElement.elementName.attr(
        "style",
        `user-select: none; overflow-wrap: break-word; font-weight: ${storedElement.textWeight}; font-size: ${storedElement.textSize}px; font-family: ${storedElement.textFont}, Helvetica, sans-serif;`,
      );

      window.getSelection().removeAllRanges();
    }
  });
  editableDiv.addEventListener("click", () => {
    editableDiv.focus();
  });
  editableDiv.addEventListener("keypress", (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      editableDiv.blur();
    }
  });
};

export const setupSegmentListener = (segment) => {
  const graphicSegment = segment.graphicSegment;

  // Helper function to check if a segment is connected to an element from an imported sequence
  const isSegmentConnectedToImportedElement = () => {
    const simpleSegment = store.state.allSimpleSegments.get(segment.id);
    if (!simpleSegment) return false;

    // Check if either of the connected elements is from an imported sequence
    return (
      isElementFromImportedSequence(
        simpleSegment.segmentElementsInfo.element1,
      ) ||
      isElementFromImportedSequence(simpleSegment.segmentElementsInfo.element2)
    );
  };

  graphicSegment
    .on("mouseover", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      // Don't allow hovering on segments connected to elements from imported sequences
      if (isSegmentConnectedToImportedElement()) {
        return;
      }
      graphicSegment.stroke({
        color: "#000",
        width: 4,
      });
      graphicSegment.attr({ cursor: "pointer" });
    })
    .on("mouseleave", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      if (!store.state.selectedSegmentsIds.includes(segment.id)) {
        graphicSegment.stroke({
          color: "#000",
          width: 2,
        });
      }
    })
    .on("click", async (e) => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      // Don't allow clicking on segments connected to elements from imported sequences
      if (isSegmentConnectedToImportedElement()) {
        return;
      }
      if (e.ctrlKey || e.metaKey) {
        addSegmentToSelection(segment.id, false);
      } else {
        addSegmentToSelection(segment.id, true);
        await unselectAllElements();
      }
    })
    .on("dblclick", () => {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      // Don't allow double-clicking on segments connected to elements from imported sequences
      if (isSegmentConnectedToImportedElement()) {
        return;
      }
      const simpleSegment = deepClone(
        store.state.allSimpleSegments.get(segment.id),
      );
      if (simpleSegment.segmentElementsInfo.mode === "LINEAR") {
        simpleSegment.segmentElementsInfo.mode = "STAIRCASE";
      } else {
        simpleSegment.segmentElementsInfo.mode = "LINEAR";
      }
      store
        .dispatch("updateSegmentAction", {
          segment: simpleSegment,
          undoable: true,
        })
        .then();
      traceSegment(
        simpleSegment.segmentElementsInfo.element1,
        simpleSegment.segmentElementsInfo.element2,
        true,
      );
    });
};

export const setupExpandedBoxAnchorsListeners = (
  anchor,
  expandedWorkspaceBox,
) => {
  anchor
    .on("dragstart", (e) => {
      e.preventDefault();
      if (store.state.diagramMode !== diagramMode.creationMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      const dragParams = store.state.dragParams;
      dragParams.dragFromAnchorStarted = true;
      dragParams.dragFromAnchorElementId = expandedWorkspaceBox.elementId;

      // Create a temporary line to show the connection being created
      const boxGroup = expandedWorkspaceBox.graphicWorkspaceBox;
      boxGroup.graphicTempLink = draw
        .line(anchor.cx(), anchor.cy(), e.detail.box.x2, e.detail.box.y2)
        .stroke({
          color: "#a5a5a5",
          width: 3,
          dasharray: 3,
        });
      boxGroup.graphicTempLink.back();
      boxGroup.graphicTempLink.forward();
    })
    .on("dragmove", (e) => {
      e.preventDefault();
      if (store.state.diagramMode !== diagramMode.creationMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }

      const boxGroup = expandedWorkspaceBox.graphicWorkspaceBox;
      if (boxGroup.graphicTempLink) {
        boxGroup.graphicTempLink.plot(
          anchor.cx(),
          anchor.cy(),
          e.detail.box.x2,
          e.detail.box.y2,
        );
      }

      // Check if we're hovering over another element's anchor
      const point = draw.point(e.detail.event.pageX, e.detail.event.pageY);
      const dragParams = store.state.dragParams;

      // Find if we're over an element's anchor
      let foundDestinationElement = false;
      drawAdapter.parameters.allElements.forEach((element) => {
        if (element.id === expandedWorkspaceBox.elementId) {
          return; // Skip the source element
        }

        const elementEntity = element.graphicElement.entity;
        if (!elementEntity) return;

        // Check each anchor
        ["topAnchor", "bottomAnchor", "leftAnchor", "rightAnchor"].forEach(
          (anchorName) => {
            const targetAnchor = elementEntity[anchorName];
            if (targetAnchor && targetAnchor.inside(point.x, point.y)) {
              dragParams.dragDestinationElement = element.id;
              foundDestinationElement = true;

              // Highlight the destination anchor
              if (!boxGroup.graphicDestinationDummy) {
                boxGroup.graphicDestinationDummy = draw
                  .circle(10)
                  .fill("#ff000055");
              }
              boxGroup.graphicDestinationDummy.center(
                targetAnchor.cx(),
                targetAnchor.cy(),
              );
            }
          },
        );
      });

      if (!foundDestinationElement && boxGroup.graphicDestinationDummy) {
        boxGroup.graphicDestinationDummy.remove();
        boxGroup.graphicDestinationDummy = null;
        dragParams.dragDestinationElement = null;
      }
    })
    .on("dragend", async (e) => {
      e.preventDefault();
      if (store.state.diagramMode !== diagramMode.creationMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }

      const boxGroup = expandedWorkspaceBox.graphicWorkspaceBox;

      // Clean up temporary graphics
      if (boxGroup.graphicTempLink) {
        boxGroup.graphicTempLink.remove();
        boxGroup.graphicTempLink = null;
      }

      if (boxGroup.graphicDestinationDummy) {
        boxGroup.graphicDestinationDummy.remove();
        boxGroup.graphicDestinationDummy = null;
      }

      // Reset drag parameters
      const dragParams = store.state.dragParams;
      const destinationElementId = dragParams.dragDestinationElement;
      dragParams.dragFromAnchorStarted = false;
      dragParams.dragFromAnchorElementId = null;
      dragParams.dragDestinationElement = null;

      // If we dropped on an anchor, create a connection
      if (
        destinationElementId &&
        destinationElementId !== expandedWorkspaceBox.elementId
      ) {
        createConnectionBetweenElements(
          expandedWorkspaceBox.elementId,
          destinationElementId,
          true,
        );
      }
    });
};

export const setupExpandedBoxListener = (expandedWorkspaceBox) => {
  const boxGroup = expandedWorkspaceBox.graphicWorkspaceBox;

  // Setup collapse button listener
  boxGroup.collapseButtonGroup
    .on("click", async () => {
      await collapseWorkspace(expandedWorkspaceBox.elementId);
    })
    .on("mouseenter", () => {
      boxGroup.collapseButton.animate(200).fill("#1976d2");
    })
    .on("mouseleave", () => {
      boxGroup.collapseButton.animate(200).fill("#3c4365");
    });

  // Setup resizer listener
  if (boxGroup.resizer) {
    boxGroup.resizer
      .on("dragstart", () => {
        // Capture initial positions when drag starts
        const element = store.state.allSimpleElements.get(
          expandedWorkspaceBox.elementId,
        );
        if (element && isWorkspaceExpanded(element.id)) {
          const childElements = Array.from(
            store.state.allSimpleElements.values(),
          ).filter((el) =>
            el.parentWorkspaceIds.includes(element.childWorkspaceId),
          );
          drawAdapter.captureInitialChildPositions(
            element.childWorkspaceId,
            childElements.map((el) => el.id),
          );
        }
      })
      .on("dragmove", async (e) => {
        if (store.state.diagramMode === diagramMode.playbackMode) {
          e.preventDefault();
          return;
        }
        if (
          store.state.sequences.get(store.state.currentSequenceId) &&
          store.state.sequences.get(store.state.currentSequenceId)
            .currentFrame %
            1 !==
            0
        ) {
          e.preventDefault();
          return;
        }

        // Calculate new width and height based on the resizer position
        const box = boxGroup.box;
        const originalX = box.x();
        const originalY = box.y();
        let newWidth = e.detail.box.x2 - originalX;
        let newHeight = e.detail.box.y2 - originalY;

        // Ensure minimum size
        if (newWidth <= 50 || newHeight <= 50) {
          return;
        }

        // Update resizer opacity
        boxGroup.resizer.opacity(1);

        // Real-time resizing during drag (visual only, don't update store)
        await resizeExpandedWorkspace(
          expandedWorkspaceBox.elementId,
          newWidth,
          newHeight,
        );

        const simpleElement = store.state.allSimpleElements.get(
          expandedWorkspaceBox.elementId,
        );
        // Retrace all segments connected to this expanded workspace
        if (
          simpleElement.workspaceParameters[store.state.currentWorkspaceId]
            .destinationElementIds &&
          simpleElement.workspaceParameters[store.state.currentWorkspaceId]
            .destinationElementIds.length > 0
        ) {
          // Find all segments that involve this workspace
          const segments = Array.from(
            store.state.allSimpleSegments.values(),
          ).filter((segment) => {
            return (
              segment.segmentElementsInfo.element1 ===
                expandedWorkspaceBox.elementId ||
              segment.segmentElementsInfo.element2 ===
                expandedWorkspaceBox.elementId
            );
          });

          // Retrace each segment
          segments.forEach((segment) => {
            const sourceId = segment.segmentElementsInfo.element1;
            const destId = segment.segmentElementsInfo.element2;
            traceSegment(sourceId, destId, true);
          });
        }
      })
      .on("dragend", async () => {
        if (store.state.diagramMode === diagramMode.playbackMode) {
          return;
        }
        if (
          store.state.sequences.get(store.state.currentSequenceId) &&
          store.state.sequences.get(store.state.currentSequenceId)
            .currentFrame %
            1 !==
            0
        ) {
          return;
        }

        // Calculate new width and height based on the resizer position
        const box = boxGroup.box;
        const originalX = box.x();
        const originalY = box.y();
        const newWidth =
          Math.round((boxGroup.resizer.x() - originalX + 15) / 10) * 10;
        const newHeight =
          Math.round((boxGroup.resizer.y() - originalY + 15) / 10) * 10;

        // Ensure minimum size
        if (newWidth <= 50 || newHeight <= 50) {
          // Reset resizer position
          boxGroup.resizer.move(
            originalX + box.width() - 15,
            originalY + box.height() - 15,
          );
          // Clear captured positions
          const element = store.state.allSimpleElements.get(
            expandedWorkspaceBox.elementId,
          );
          if (element && isWorkspaceExpanded(element.id)) {
            drawAdapter.clearInitialChildPositions(element.childWorkspaceId);
          }
          return;
        }

        // Final resize with store update (snap to grid)
        await resizeExpandedWorkspace(
          expandedWorkspaceBox.elementId,
          newWidth,
          newHeight,
        );

        // Update reference dimensions and clear captured positions after successful resize
        const element = store.state.allSimpleElements.get(
          expandedWorkspaceBox.elementId,
        );
        if (element && isWorkspaceExpanded(element.id)) {
          drawAdapter.updateExpandedWorkspaceReferences(
            element.childWorkspaceId,
          );
          drawAdapter.clearInitialChildPositions(element.childWorkspaceId);
        }
        // Retrace all segments connected to this expanded workspace
        const simpleElement = store.state.allSimpleElements.get(
          expandedWorkspaceBox.elementId,
        );
        if (
          simpleElement.workspaceParameters[store.state.currentWorkspaceId]
            .destinationElementIds &&
          simpleElement.workspaceParameters[store.state.currentWorkspaceId]
            .destinationElementIds.length > 0
        ) {
          // Find all segments that involve this workspace
          const segments = Array.from(
            store.state.allSimpleSegments.values(),
          ).filter((segment) => {
            return (
              segment.segmentElementsInfo.element1 ===
                expandedWorkspaceBox.elementId ||
              segment.segmentElementsInfo.element2 ===
                expandedWorkspaceBox.elementId
            );
          });

          // Retrace each segment
          segments.forEach((segment) => {
            const sourceId = segment.segmentElementsInfo.element1;
            const destId = segment.segmentElementsInfo.element2;
            traceSegment(sourceId, destId, true);
          });
        }
      })
      .on("mouseover", () => {
        if (store.state.diagramMode === diagramMode.playbackMode) {
          return;
        }
        if (
          store.state.sequences.get(store.state.currentSequenceId) &&
          store.state.sequences.get(store.state.currentSequenceId)
            .currentFrame %
            1 !==
            0
        ) {
          return;
        }
        boxGroup.resizer.attr({ cursor: "se-resize" });
      });
  }

  // Setup dragging behavior for the box
  boxGroup.on("dragmove", async () => {
    // When the box is moved, we need to update the anchors and retrace segments
    // No need to update expanded workspace data since we're using element-based storage now
    const simpleElement = store.state.allSimpleElements.get(
      expandedWorkspaceBox.elementId,
    );

    if (simpleElement && isWorkspaceExpanded(simpleElement.id)) {
      // Get the new position from the graphic element
      const newX = boxGroup.box.cx();
      const newY = boxGroup.box.cy();

      // Update the element's position in state
      await updateExpandedWorkspaceElementPosition(
        simpleElement.id,
        newX,
        newY,
        true,
      );

      drawAdapter.updateExpandedWorkspaceReferences(
        simpleElement.childWorkspaceId,
      );
    }

    // Retrace all segments connected to this expanded workspace
    if (
      simpleElement.workspaceParameters[store.state.currentWorkspaceId]
        .destinationElementIds &&
      simpleElement.workspaceParameters[store.state.currentWorkspaceId]
        .destinationElementIds.length > 0
    ) {
      // Find all segments that involve this workspace
      const segments = Array.from(
        store.state.allSimpleSegments.values(),
      ).filter((segment) => {
        return (
          segment.segmentElementsInfo.element1 ===
            expandedWorkspaceBox.elementId ||
          segment.segmentElementsInfo.element2 ===
            expandedWorkspaceBox.elementId
        );
      });

      // Retrace each segment
      segments.forEach((segment) => {
        const sourceId = segment.segmentElementsInfo.element1;
        const destId = segment.segmentElementsInfo.element2;
        traceSegment(sourceId, destId, true);
      });
    }
  });

  boxGroup.on("dragend", async () => {
    // Update reference position after moving the box
    const element = store.state.allSimpleElements.get(
      expandedWorkspaceBox.elementId,
    );
    if (element && isWorkspaceExpanded(element.id)) {
      // Get the new position from the graphic element
      const newX = boxGroup.box.cx();
      const newY = boxGroup.box.cy();

      // Update the element's position in state
      await updateExpandedWorkspaceElementPosition(
        element.id,
        newX,
        newY,
        true,
      );

      drawAdapter.updateExpandedWorkspaceReferences(element.childWorkspaceId);
    }

    // Retrace all segments connected to this expanded workspace
    if (
      expandedWorkspaceBox.destinationElementIds &&
      expandedWorkspaceBox.destinationElementIds.length > 0
    ) {
      // Find all segments that involve this workspace
      const segments = Array.from(
        store.state.allSimpleSegments.values(),
      ).filter((segment) => {
        return (
          segment.segmentElementsInfo.element1 ===
            expandedWorkspaceBox.elementId ||
          segment.segmentElementsInfo.element2 ===
            expandedWorkspaceBox.elementId
        );
      });

      // Retrace each segment
      segments.forEach((segment) => {
        const sourceId = segment.segmentElementsInfo.element1;
        const destId = segment.segmentElementsInfo.element2;
        traceSegment(sourceId, destId, true);
      });
    }
  });

  // Setup anchor listeners
  setupExpandedBoxAnchorsListeners(boxGroup.topAnchor, expandedWorkspaceBox);
  setupExpandedBoxAnchorsListeners(boxGroup.bottomAnchor, expandedWorkspaceBox);
  setupExpandedBoxAnchorsListeners(boxGroup.leftAnchor, expandedWorkspaceBox);
  setupExpandedBoxAnchorsListeners(boxGroup.rightAnchor, expandedWorkspaceBox);
};

document.addEventListener(
  "keydown",
  async function (e) {
    if (
      (e.key === "Delete" || e.key === "Backspace") &&
      document.activeElement.tagName === "BODY"
    ) {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      store.getters.currentWorkspaceSelectedElementsIds.forEach((elementId) => {
        deleteElement({ elementId, undoable: true });
      });
      store.state.selectedSegmentsIds.forEach((segmentId) => {
        deleteSegment(segmentId, false, true);
      });
    } else if (
      (e.ctrlKey || e.metaKey) &&
      e.key === "v" &&
      document.activeElement.tagName === "BODY"
    ) {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      await cloneSelectedElements();
    } else if (
      (e.ctrlKey || e.metaKey) &&
      e.key === "c" &&
      document.activeElement.tagName === "BODY"
    ) {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      if (
        store.state.sequences.get(store.state.currentSequenceId) &&
        store.state.sequences.get(store.state.currentSequenceId).currentFrame %
          1 !==
          0
      ) {
        return;
      }
      await store.dispatch(
        "updateCopyElementIdsAction",
        store.getters.currentWorkspaceSelectedElementsIds,
      );
    } else if ((e.ctrlKey || e.metaKey) && e.key === "s") {
      e.preventDefault();
      saveWorkspace().then();
    } else if (
      (e.ctrlKey || e.metaKey) &&
      !e.shiftKey &&
      e.key === "z" &&
      document.activeElement.tagName === "BODY"
    ) {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      e.preventDefault();
      await undo();
    } else if (
      (e.ctrlKey || e.metaKey) &&
      !e.shiftKey &&
      e.key === "a" &&
      document.activeElement.tagName === "BODY"
    ) {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      e.preventDefault();
      await selectAllElements();
    } else if (
      (e.ctrlKey || e.metaKey) &&
      e.shiftKey &&
      e.key === "Z" &&
      document.activeElement.tagName === "BODY"
    ) {
      if (store.state.diagramMode === diagramMode.playbackMode) {
        return;
      }
      e.preventDefault();
      await redo();
    } else if (
      (e.key === " " || e.code === "Space") &&
      document.activeElement.tagName === "BODY" &&
      (store.state.diagramMode === diagramMode.recordingMode ||
        store.state.diagramMode === diagramMode.playbackMode)
    ) {
      const sequence = store.state.sequences.get(store.state.currentSequenceId);
      if (sequence.isPlaying) {
        pauseSequence(sequence.id).then();
      } else {
        // setupObjectsAtFrame(sequence.id, sequence.currentSequenceFrame);
        await playSequence(sequence.id).then();
      }
    }
  },
  false,
);
